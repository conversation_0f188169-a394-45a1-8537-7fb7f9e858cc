
#ifndef FILE_H
#define FILE_H

#include "manage.h"

#define MAX_SYNC_LEN 500

typedef enum
{
    ATWR = 0x0,    // 用于存日志文件，在后面追加
    WTWR = 0x01,   // 用于存储接收的文件，若有原文件清0
    ONLY_R = 0x02, // 用于读取要发送的文件

} enum_file_mode;

extern long sync_len;
extern FILE *pLogFile, *presultFile;
extern char self_log_file[], log_reorg_file[];
extern char mem_test[];

int Func_Dprintf(char *destpoint, char *fmt, ...);
void file_add_full_fath(char *dest, char *src, char *name);
FILE *file_open(char *path, enum_file_mode mode);
void file_write_data(FILE *pfile, char *scr_ata, int len);
void file_write_time_data(FILE *pfile, char *scr_ata, int len);
void file_write_fmt(FILE *pfile, char *fmt, ...);
void file_write_time_fmt(FILE *pfile, char *fmt, ...);
void file_write_head(FILE *pfile);
int file_read(FILE *pfile, char *scr_ata, int len, long location);
void file_close(FILE *pfile);
void file_add(FILE *des, FILE *scr);
int file_read_last_n_data(FILE *pfile, char *scr_ata, int len, int location);
int Func_Time_GetSystemTime_ToChar(char *TimeChar);
int Func_Time_GetSystemTime_ToChar1(char *TimeChar);
void func_my_print(char *buff, unsigned int lens, unsigned char mode);

#endif