#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <sys/time.h>
#include <time.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "file.h"
#include "manage.h"

/*
 * @description       : 要存文件的字符串按照格式放到带来的字符串指针里
 * @param - *destpoint: 用于存放格式化好的字符串
 * @param - *fmt, ... ：格式化方式
 * @return		      : 字符串长度
 */
int Func_Dprintf(char *destpoint, char *fmt, ...)
{
    va_list arg_ptr;
    char uLen, *tmpBuf;

    tmpBuf = destpoint;
    va_start(arg_ptr, fmt);
    uLen = vsprintf(tmpBuf, fmt, arg_ptr);

    va_end(arg_ptr); // 用va_end宏结束可变参数的获取
    return uLen;
}
/*
 * @description    : 给文件补全路径
 * @param - *dest  ：补全路径后的文件名
 * @param - *src   ：当前路径
 * @param - *name  ：原文件名
 * @return		   : 无
 */
void file_add_full_fath(char *dest, char *src, char *name)
{
    int len1 = 0, len2;

    memset(dest, 0, strlen(dest));
    len1 = strlen(src);
    memcpy(dest, src, len1);
    len2 = strlen(name);
    memcpy(dest + len1, name, len2);
}
/*
 * @description      : 获取当前时间，并转为字符串，放到带来的字符串指针里
 * @param - *TimeChar: 用于存放时间字符串
 * @return		     : 时间字符串长度
 */
int Func_Time_GetSystemTime_ToChar(char *TimeChar)
{
    // time_t Second = 0;
    struct timeval Time;
    struct tm *pSystime = NULL;
    int TimeLen = 0;
    char TempBuf[50];

    gettimeofday(&Time, NULL);
    pSystime = localtime(&Time.tv_sec);
    memset(TempBuf, 0, sizeof(TempBuf));
    strcat(TempBuf, "\n%04d-%02d-%02d %02d:%02d:%02d");
    TimeLen = Func_Dprintf(TimeChar, TempBuf, pSystime->tm_year + 1900, pSystime->tm_mon + 1,
                           pSystime->tm_mday, pSystime->tm_hour, pSystime->tm_min, pSystime->tm_sec);

    return TimeLen;
}
/*
 * @description      : 获取当前时间，并转为字符串，放到带来的字符串指针里
 * @param - *TimeChar: 用于存放时间字符串
 * @return		     : 时间字符串长度
 */
int Func_Time_GetSystemTime_ToChar1(char *TimeChar)
{
    // time_t Second = 0;
    struct timeval Time;
    struct tm *pSystime = NULL;
    int TimeLen = 0;
    char TempBuf[50];

    gettimeofday(&Time, NULL);
    pSystime = localtime(&Time.tv_sec);
    memset(TempBuf, 0, sizeof(TempBuf));
    strcat(TempBuf, "%04d-%02d-%02d_%02d-%02d-%02d");
    TimeLen = Func_Dprintf(TimeChar, TempBuf, pSystime->tm_year + 1900, pSystime->tm_mon + 1,
                           pSystime->tm_mday, pSystime->tm_hour, pSystime->tm_min, pSystime->tm_sec);

    return TimeLen;
}
/*
 * @description : 打开文件
 * @param - path: 文件路径
 * @return		: 文件结构指针
 */
FILE *file_open(char *path, enum_file_mode mode)
{
    FILE *pfile = NULL;

    if (ATWR == mode)
        pfile = fopen(path, "at+");
    else if (WTWR == mode)
        pfile = fopen(path, "wt+");
    else
        pfile = fopen(path, "r");
    /*
    if (pfile == NULL)
    {
        printf("File Open Fail!\n");
    }
    else
        printf("File Open %s OK!\n", path);*/
    return pfile;
}

/*
 * @description    : 写文件
 * @param - pfile  : 文件结构指针
 * @param - scr_ata: 要写入文件的数据
 * @param - len    : 要写入文件数据长度
 * @return		   : 无
 */
void file_write_data(FILE *pfile, char *scr_ata, int len)
{
    fseek(pfile, 0, SEEK_END);
    fwrite(scr_ata, sizeof(char), len, pfile); /*写入数据*/

    sync_len += len;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写文件，带时间戳
 * @param - pfile  : 文件结构指针
 * @param - scr_ata: 要写入文件的数据
 * @param - len    : 要写入文件数据长度
 * @return		   : 无
 */
void file_write_time_data(FILE *pfile, char *scr_ata, int len)
{
    char Time[50] = {0};
    int time_len = 0;

    time_len = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), time_len, pfile); // 写入时间
    fwrite(scr_ata, sizeof(char), len, pfile);   /*写入数据*/

    sync_len += len;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写格式化内容到文件，内容无时间戳
 * @param - pfile  : 文件结构指针
 * @param - *fmt   : 日志内容
 * @return		   : 无
 */
void file_write_fmt(FILE *pfile, char *fmt, ...)
{
    va_list arg_ptr;
    char buffer[500] = {0};
    int uLen = 0;

    va_start(arg_ptr, fmt);
    uLen = vsprintf(buffer, fmt, arg_ptr);
    va_end(arg_ptr); // 用va_end宏结束可变参数的获取

    fwrite(buffer, sizeof(char), uLen, pfile); /*写入数据*/
    sync_len += uLen;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description    : 写log日志，内容前增加时间戳
 * @param - pfile  : 文件结构指针
 * @param - *fmt   : 日志内容
 * @return		   : 无
 */
void file_write_time_fmt(FILE *pfile, char *fmt, ...)
{
    va_list arg_ptr;
    char buffer[500] = {0};
    char Time[50] = {0};
    int uLen = 0, time_len = 0;

    va_start(arg_ptr, fmt);
    uLen = vsprintf(buffer, fmt, arg_ptr);
    va_end(arg_ptr); // 用va_end宏结束可变参数的获取

    time_len = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), time_len, pfile); // 写入时间
    fwrite(buffer, sizeof(char), uLen, pfile);   /*写入数据*/

    sync_len += time_len + uLen;
    if (sync_len > MAX_SYNC_LEN)
    {
        fflush(pfile); /*将数据同步至ROM*/
        sync_len = 0;
    }
}
/*
 * @description     : 将总执行轮次写入文件
 * @param - *pfile  : 文件结构指针
 * @param - *infos  : 测试信息
 * @return		    : 无
 */
void file_write_head(FILE *pfile)
{
    char Time[50] = {0};
    char data[100] = {0};
    int len = 0;
    int Timelen = 0;

    fwrite("\n\n**************************************************\n", sizeof(char), 53, pfile); /*写入数据*/
    printf("\n\n**************************************************\n");
    len = Func_Dprintf(data, "*** starts times is %d , whole execute times is %d ***\n", 1, 1);
    fwrite(data, sizeof(char), len, pfile); /*写入数据*/
    printf("%s", data);
    memset(data, 0, sizeof(data));
    fwrite("** ", sizeof(char), 3, pfile); /*写入数据*/
    Timelen = Func_Time_GetSystemTime_ToChar(Time);
    fwrite(Time, sizeof(char), Timelen, pfile); // 写入时间
    fwrite(" **\n", sizeof(char), 4, pfile);
    fwrite("**************************************************\n", sizeof(char), 51, pfile); /*写入数据*/
    printf("** %s **\n", Time);                                                              /*写入数据*/
    printf("**************************************************\n");

    fflush(pfile); /*将数据同步至ROM*/
}

/*
 * @description     : 读文件
 * @param - *pfile  : 文件结构指针
 * @param - *scr_ata: 要读入文件的数据缓冲区首地址
 * @param - len     : 要读入文件数据最大长度
 * @param - location: 指定文件读入位置
 * @return		    : 读入文件实际长度
 */
int file_read(FILE *pfile, char *scr_ata, int len, long location)
{
    int read_len = 0;

    fseek(pfile, location, SEEK_SET);
    read_len = fread(scr_ata, sizeof(char), len, pfile); /*读出数据*/

    return read_len;
}

/*
 * @description     : 读文件最后多少字节
 * @param - *pfile  : 文件结构指针
 * @param - *scr_ata: 要读入文件的数据缓冲区首地址
 * @param - len     : 要读入文件数据最大长度
 * @param - location: 指定文件读入位置
 * @return		    : 读入文件实际长度
 */
int file_read_last_n_data(FILE *pfile, char *scr_ata, int len, int location)
{
    int read_len = 0;

    if (0 == fseek(pfile, location, SEEK_END))
    {
        read_len = fread(scr_ata, sizeof(char), len, pfile); /*读出数据*/
    }
    else
        read_len = -1;

    return read_len;
}

/*
 * @description    : 关闭文件
 * @return		   : 无
 */
void file_close(FILE *pfile)
{
    fclose(pfile);
}

/*
 * @description    : 将文件2数据追加到文件1最后。
 * @param - *des   : 目标文件1
 * @param - *scr   : 原文件2
 * @return		   : 无
 */
void file_add(FILE *des, FILE *scr)
{
    char temp_data[500] = {0};
    int data_len = 0, i = 0;

    // lseek(des, 0, SEEK_END);
    // lseek(scr, 0, SEEK_SET);
    data_len = file_read(scr, temp_data, 500, 0);
    while (data_len)
    {
        file_write_data(des, temp_data, data_len);
        i++;
        data_len = file_read(scr, temp_data, 500, (i * 500));
    }
    fflush(des); /*将数据同步至ROM*/
}
