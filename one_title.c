
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <ctype.h>
#include <sys/epoll.h>
#include <poll.h>
#include <pthread.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/types.h>
#include <sys/wait.h>
#include "config_file.h"
#include "manage.h"
#include "file.h"
#include "serial.h"
#include "display.h"
#include "md5.h"

/* 串口接收管理线程，历史文件读写锁 */
pthread_mutex_t mutex; // 串口接收锁访问文件锁
char send_buff[MAX_RX_BUF_LEN], receive_buff[MAX_TX_BUF_LEN];
long long read_point = 0;
int print_debug = 0;
int save_no_time = 0;

/*
 * @description    : 获取时间ms
 * @param -        : 无
 * @return		   : 当前时间总毫秒数
 */
unsigned long func_get_system_time_ms(void)
{
    struct timespec time = {0, 0};

    clock_gettime(CLOCK_MONOTONIC, &time);
    return (time.tv_sec * 1000 + time.tv_nsec / 1000000);
}
/*
 * @description    : 祛除字符串中的颜色
 * @param -  *str  : 无
 * @return		   : 无
 */
void remove_str_colors(char *s)
{
    char *p1 = NULL, *p2 = NULL, *p3 = NULL;
    char temp[500] = {0};
    int current = 0, len = 0;

    /* 去除开头的空白 */
    while (isspace(*s))
        s++;
    current = 0;
    p3 = s;
    while (1)
    {
        p1 = strstr(p3, "\e[");
        p2 = strstr(p3, "m");
        if ((p1 != NULL) && (p2 != NULL) && ((p2 - p1) < 8) && ((p2 - p1) > 0))
        {
            len = (p1 - p3);
            strncpy(&temp[current], p3, len);
            current += len;
            p3 = (p2 + 1);
        }
        else
            break;
    }
    strcpy(&temp[current], p3);
    memset(s, 0, strlen(s));
    strcpy(s, temp);

    return;
}
/*
 * @description      : 统一打印测试结果，存储测试数据
 * @param - *result  : OK ERR DATA
 * @param - *numb    : 平台维护测试编号
 * @param - *mesage  : 测试项目
 * @return		     : 当前时间总毫秒数
 */
void bsp_print_save(char *result, char *mesage)
{
    if (strstr(result, "OK") != NULL)
        printf(L_GREEN "[OK]" NONE);
    else if (strstr(result, "ERR") != NULL)
        printf(L_RED "[ERR]" NONE);
    else
        printf(L_BLUE "[DATA]" NONE);
    printf(" %s\n", mesage);
    file_write_fmt(presultFile, "[%s], %s\n", result, mesage);
}
/*
 * @description      : 字符串替换
 * @param - *str     : 原字符串
 * @param - *old_sub : 需要被替换的字符串
 * @param - *new_sub : 替换的字符串
 * @return		     : 无
 */
void replace_substring(char *str, const char *old_sub, const char *new_sub)
{
    char buffer[MAX_CMD_LEN * 2];
    char *pos;

    // 清空缓冲区
    memset(buffer, 0, sizeof(buffer));

    // 找到第一个匹配的位置
    pos = strstr(str, old_sub);
    while (pos != NULL)
    {
        // 复制匹配位置之前的部分到缓冲区
        strncpy(buffer, str, pos - str);
        buffer[pos - str] = '\0';

        // 追加替换字符串
        strcat(buffer, new_sub);

        // 追加匹配位置之后的部分
        strcat(buffer, pos + strlen(old_sub));

        // 将缓冲区内容复制回原字符串
        strcpy(str, buffer);

        // 查找下一个匹配位置
        pos = strstr(str, old_sub);
    }
}
/*
 * @description       : 将命令里参数替换掉。
 * @param - test_item : 测试哪一项
 * @param - group     : 第几组
 * @param - num       : 第几个line
 * @return		      : 无
 */
void bsp_line_replace_var(struct_test_item *test_item, int group, int num)
{
    int i = 0;
    char temp1[10] = {0};
    char temp2[MAX_DATA_STR_LEN] = {0};

    // 检查指针是否有效
    if (test_item == NULL || group < 0 || group >= MAX_GROUP ||
        num < 0 || num >= test_item->group[group].line_count ||
        test_item->group[group].line == NULL)
    {
        return;
    }

    if (strstr(test_item->group[group].line[num].content, "@app_path") != NULL)
    {
        replace_substring(test_item->group[group].line[num].content, "@app_path", test->app_path);
    }

    for (i = 0; i < test_item->param_count; i++)
    {
        sprintf(temp1, "@param%d_", (i + 1));
        if (strstr(test_item->group[group].line[num].content, temp1) != NULL)
        {
            if (test_item->param[i].type == DATA_INT)
                sprintf(temp2, "%d", test_item->param[i].data_int);
            else if (test_item->param[i].type == DATA_FLOAT)
                sprintf(temp2, "%f", test_item->param[i].data_float);
            else
                strcpy(temp2, test_item->param[i].data_str);
            replace_substring(test_item->group[group].line[num].content, temp1, temp2);
        }
    }
    for (i = 0; i < VALUE_NUMB; i++)
    {
        sprintf(temp1, "@res%d_", (i + 1));
        if (strstr(test_item->group[group].line[num].content, temp1) != NULL)
        {
            if (strstr(test_item->res[i].res, "fassed") != NULL)
                sprintf(temp2, "%d", 1);
            else
                sprintf(temp2, "%d", 0);
            replace_substring(test_item->group[group].line[num].content, temp1, temp2);
        }
    }
    for (i = 0; i < VALUE_NUMB; i++)
    {
        sprintf(temp1, "@data%d_", (i + 1));
        if (strstr(test_item->group[group].line[num].content, temp1) != NULL)
        {
            if (test_item->data[i].type == DATA_INT)
                sprintf(temp2, "%d", test_item->data[i].data_int);
            else if (test_item->data[i].type == DATA_FLOAT)
                sprintf(temp2, "%f", test_item->data[i].data_float);
            else
                strcpy(temp2, test_item->data[i].data_str);
            replace_substring(test_item->group[group].line[num].content, temp1, temp2);
        }
    }
}
/*
 * @description         : 祛除退格
 * @param -  *inputFile : 源文件指针
 * @param -  *outputFile: 新文件指针
 * @return		        : 无
 */
void bsp_remove_backspace(FILE *inputFile, FILE *outputFile)
{
    char currentChar, prevChar;
    prevChar = EOF; // 初始时上一个字符为文件结束符

    // 逐字符读取输入文件
    // while ((currentChar = fgetc(inputFile)) != EOF)
    while (1)
    {
        currentChar = fgetc(inputFile);
        if ((currentChar <= 0) || (currentChar == 0xff))
            return;
        if (currentChar == '\b')
        { // 如果当前字符是退格转义符
            if (prevChar != EOF)
            {
                // 将光标移到前一个位置
                fseek(outputFile, -1, SEEK_CUR);
                prevChar = fgetc(outputFile);

                // 覆盖前一个字符
                fseek(outputFile, -1, SEEK_CUR);
                fputc(' ', outputFile);

                // 将光标移到下一个位置
                fseek(outputFile, -1, SEEK_CUR);
            }
        }
        else
        {
            fputc(currentChar, outputFile);
            prevChar = currentChar;
        }
    }
    return;
}
/*
 * @description    : 整理文件格式
 * @param - pfile  : 原文件结构指针
 * @return		   : 无
 */
void file_reorg_data(FILE *pfile)
{
    char temp_log_reorg_file[100] = {0};
    char line[500] = {0};
    FILE *temp_preLogFile = NULL, *preLogFile = NULL;

    // 去掉颜色
    sprintf(temp_log_reorg_file, "%s_temp", log_reorg_file);
    temp_preLogFile = file_open(temp_log_reorg_file, WTWR);
    if (NULL == temp_preLogFile)
        return;

    fseek(pfile, 0, SEEK_SET);

    while (!feof(pfile))
    {
        memset(line, 0, 500);
        if (fgets(line, 500, pfile) != NULL)
        {
            if (strstr(line, "\e[") != NULL)
                remove_str_colors(line);

            fwrite(line, sizeof(char), strlen(line), temp_preLogFile); /*写入数据*/
        }
    }
    fflush(temp_preLogFile); /*将数据同步至ROM*/
    // 去掉退格
    preLogFile = file_open(log_reorg_file, WTWR);
    if (NULL == preLogFile)
    {
        file_close(temp_preLogFile);
        return;
    }
    fseek(temp_preLogFile, 0, SEEK_SET);
    bsp_remove_backspace(temp_preLogFile, preLogFile);
    fflush(preLogFile); /*将数据同步至ROM*/
    file_close(temp_preLogFile);
    file_close(preLogFile);
    unlink(temp_log_reorg_file);
}

/*
 * @description      : 通过执行命令获取pid的值
 * @param - *command : 参数
 * @return		     : pid号
 */
int get_command_pid(char *command, int numb)
{
    FILE *fstream = NULL;
    char data[250] = {0};
    char tesult[20] = {0};
    int pid_value = 0;
    char command_new[200] = {0};
    int i = 0;

    if (NULL != strstr(command, " &"))
    {
        memcpy(command_new, command, (strlen(command) - strlen(" &")));
    }
    else
        strcpy(command_new, command);

    sprintf(data, "ps -ax 2>/dev/null | grep '%s' | awk '{print $1}'", command_new);
    if (NULL == (fstream = popen(data, "r")))
    {
        fprintf(stderr, "execute command_new failed: %s", strerror(errno));
    }
    while ((fgets(tesult, sizeof(tesult) - 1, fstream)) != NULL)
    {
        pid_value = atoi(tesult);
        i++;
        if (i > numb)
            break;
    }
    pclose(fstream);

    if (0 == pid_value)
    {
        memset(data, 0, 200);
        sprintf(data, "ps |grep '%s' | awk '{print $1}'", command_new);
        if (NULL == (fstream = popen(data, "r")))
        {
            fprintf(stderr, "execute command_new failed: %s", strerror(errno));
        }
        while ((fgets(tesult, sizeof(tesult) - 1, fstream)) != NULL)
        {
            pid_value = atoi(tesult);
            i++;
            if (i > numb)
                break;
        }
        pclose(fstream);
    }
    return pid_value;
}
/*
 * @description         : 确认该pid进程是否还存在
 * @param - command_pid : 参数
 * @return		        : 0-进程不存在 1-进程存在
 */
int check_command_pid(int command_pid)
{
    char path[50] = {0};

    sprintf(path, "/proc/%d", command_pid);
    if ((access(path, F_OK)) == 0)
    {
        return 1;
    }
    else
    {
        usleep(200000);
        printf("pid=%s,process end\n", path);
        return 0;
    }
    return 0;
}
/*
 * @description : 根据pid杀死进程组
 * @param - pids: 该命令pid
 * @return		: 0 ：执行成功 -1 ；执行失败
 */
void kill_command_pid(int pids)
{
    FILE *fstream = NULL;
    char data[200] = {0};

    Func_Dprintf(data, "kill -9 %d", pids);

    if (NULL == (fstream = popen(data, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    pclose(fstream);
}
/*
 * @description : 根据关键字杀死进程
 * @param - *cmd: 关键字
 * @return		: 0 ：执行成功 -1 :执行失败
 */
void kill_command_kw(char *cmd)
{
    FILE *fstream = NULL;
    char data[200] = {0};
    int pids = 0;

    pids = get_command_pid(cmd, 0);
    sprintf(data, "kill -9 %d", pids);

    if (NULL == (fstream = popen(data, "w")))
    {
        fprintf(stderr, "execute command failed: %s", strerror(errno));
    }
    pclose(fstream);
}
/*
 * @description    : 自己的popen
 * @param - command: 要执行的命令
 * @param - type   : 类型 读是0，写是1
 * @param - & pid  : 该命令的pid指针
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
FILE *popen2(char *command, char type, int *pid)
{
    pid_t child_pid;
    int fd[2];
    pipe(fd);

    if ((child_pid = fork()) == -1)
    {
        perror("fork");
        exit(1);
    }

    /* child process */
    if (child_pid == 0)
    {
        if (type == 'r')
        {
            close(fd[READ]);    // Close the READ end of the pipe since the child's fd is write-only
            dup2(fd[WRITE], 1); // Redirect stdout to pipe
        }
        else
        {
            close(fd[WRITE]);  // Close the WRITE end of the pipe since the child's fd is read-only
            dup2(fd[READ], 0); // Redirect stdin to pipe
        }

        setpgid(child_pid, child_pid); // Needed so negative PIDs can kill children of /bin/sh
        execl("/bin/sh", "/bin/sh", "-c", command, NULL);
        exit(0);
    }
    else
    {
        if (type == 'r')
        {
            close(fd[WRITE]); // Close the WRITE end of the pipe since parent's fd is read-only
        }
        else
        {
            close(fd[READ]); // Close the READ end of the pipe since parent's fd is write-only
        }
    }

    *pid = child_pid;

    if (type == 'r')
    {
        return fdopen(fd[READ], "r");
    }

    return fdopen(fd[WRITE], "w");
}
/*
 * @description    : 自己的pclose
 * @param - * fp   : 管道指针
 * @param - pid    : 该命令的pid
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
int pclose2(FILE *fp, pid_t pid)
{
    int stat = 0;

    fclose(fp);
    while (waitpid(pid, &stat, 0) == -1)
    {
        if (errno != EINTR)
        {
            stat = -1;
            break;
        }
    }

    return stat;
}
/*
 * @description    : 自己的system
 * @param - command: 要执行的命令
 * @param - & pid  : 该命令的pid指针
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
void bsp_my_system(char *command, int *pid)
{
    FILE *fp = NULL;
    int real_pid = 0;
    char temp_cmd[100] = {0};
    int i = 0;

    fp = popen2(command, READ, pid);
    if (fp == NULL)
    {
        printf("%s execution error\n", command);
        return;
    }
    else
    {
        pclose2(fp, *pid);
    }
    usleep(20000);

    i = 0;
    sscanf(command, "%[^ ]", temp_cmd);
    while (*pid > real_pid)
    {
        real_pid = get_command_pid(temp_cmd, i);
        if (0 == real_pid)
            break;
        i++;
        if (i > MAX_EXE_ITEM)
            break;
    }
    *pid = real_pid;
    printf("cmd=%s,pid=%d\n", command, real_pid);
}

/*
 * @description    : 获取随机字符串
 * @param - *src   : 数据指针
 * @param - len    : 所需数据长度
 * @return		   : 无
 */
void func_get_rand_char(char *src, int len)
{
    struct timespec time = {0, 0};
    int i = 0;

    for (i = 0; i < len; i++)
    {
        clock_gettime(CLOCK_MONOTONIC, &time);
        srand(time.tv_nsec);
        *(src + i) = rand() % 255;
        if (0x20 == *(src + i))
            (*(src + i)) = (*(src + i)) + 1;
        if (0x20 == *(src + i))
            printf("!!!!!!!!!!!!!0x20\n");
    }
}
/*
 * @description    : 将读指针指到文件末尾
 * @return		   : 无
 */
void bsp_sync_read_point(void)
{
    pthread_mutex_lock(&mutex);
    fseek(pLogFile, 0, SEEK_END);
    read_point = ftell(pLogFile);
    pthread_mutex_unlock(&mutex);
    // printf("bsp_sync_read_point read_point=%lld\n", read_point);
}
/*
 * @description    : 确认是否有指定字符串
 * @param - *str   : 要判别的字符串
 * @param -sync_read_point ：判别完成后是否将读文件指到文件末尾 0-不同步 1-同步
 * @return		   : 0-无 1- 有
 */
int bsp_check_keyword(char *str, enum_test_sync sync_read_point)
{
    char line[256] = {0};

    if (*str == 0)
        return 0;
    pthread_mutex_lock(&mutex);
    fseek(pLogFile, read_point, SEEK_SET);
    while (!feof(pLogFile))
    {
        memset(line, 0, 256);
        if (fgets(line, 256, pLogFile) != NULL)
        {
            if (strstr(line, str) != NULL)
            {
                if (sync_read_point == READ_POINT_FIND_KW_CURRENT)
                {
                    fseek(pLogFile, 0, SEEK_CUR);
                    read_point = ftell(pLogFile);
                }
                if ((sync_read_point == READ_POINT_FIND_KW_SYNC) || (sync_read_point == READ_POINT_SYNC))
                {
                    fseek(pLogFile, 0, SEEK_END);
                    read_point = ftell(pLogFile);
                }
                pthread_mutex_unlock(&mutex);
                return 1;
            }
        }
    }
    if ((sync_read_point == READ_POINT_NO_KW_SYNC) || (sync_read_point == READ_POINT_SYNC))
    {
        fseek(pLogFile, 0, SEEK_END);
        read_point = ftell(pLogFile);
    }
    pthread_mutex_unlock(&mutex);
    return 0;
}
/*
 * @description            : 确认是否有指定字符串
 * @param - *src_File      : 要判别的文件指针
 * @param - src_read_point : 文件指向位置
 * @param - *res           : 要判别的字符串结构体
 * @return		           : 0-无 1- 有pass keyword -1-找到错误关键字
 */
int bsp_check_mul_keyword(FILE *src_File, long long src_read_point, struct_get_result_form *res)
{
    char line[256] = {0};
    int i = 0;

    fseek(src_File, src_read_point, SEEK_SET);
    while (!feof(src_File))
    {
        memset(line, 0, 256);
        if (fgets(line, 256, src_File) != NULL)
        {
            if ((res->modle == ALL_SCAN) || (res->modle == ALL_SCAN_ERR_KW_OK))
            {
                for (i = 0; i < res->ew_count; i++)
                {
                    if (strstr(line, res->err_word[i]) != NULL)
                    {
                        return -1;
                    }
                }
            }
            if (res->modle == ALL_SCAN)
            {
                for (i = 0; i < res->kw_count; i++)
                {
                    if (strstr(line, res->pass_word[i]) != NULL)
                    {
                        return 1;
                    }
                }
            }
            if (res->modle == KEYWORD_SCAN)
            {
                if (strstr(line, res->key_word) != NULL)
                {
                    for (i = 0; i < res->ew_count; i++)
                    {
                        if (strstr(line, res->err_word[i]) != NULL)
                        {
                            return -1;
                        }
                    }
                    for (i = 0; i < res->kw_count; i++)
                    {
                        if (strstr(line, res->pass_word[i]) != NULL)
                        {
                            return 1;
                        }
                    }
                }
            }
        }
    }
    return 0;
}
/*
 * @description            : 获取所需字符串
 * @param - *src_File      : 要判别的文件指针
 * @param - src_read_point : 文件指向位置
 * @param - *from          : 要判别的字符串
 * @param - *data          : 测试结果字符串
 * @return		           : 0-无 1- 有 -1-找到错误关键字
 */
int bsp_get_value(FILE *src_File, long long src_read_point, struct_get_value_form *from, struct_data_class *data)
{
    int point = 0, need_formate = 0;
    char data_form[15][64];
    int x = 0, y = 0, wait_x = 0, len = 0;
    char line[500] = {0};
    char *p1 = NULL;

    memset(data_form, 0, sizeof(data_form));
    x = from->x;
    y = from->y;
    fseek(src_File, src_read_point, SEEK_SET);
    while (!feof(src_File))
    {
        memset(line, 0, 500);
        if (fgets(line, 500, src_File) != NULL)
        {
            // 判断行是否只包含回车换行符
            int only_newline = 1;
            for (int i = 0; line[i] != '\0'; i++)
            {
                if (line[i] != '\r' && line[i] != '\n' && line[i] != ' ' && line[i] != '\t')
                {
                    only_newline = 0;
                    break;
                }
            }
            // 如果行只包含回车换行符或空白字符，则跳过此行
            if (only_newline)
            {
                continue;
            }
            // printf("line=%s\n", line);
            if ((from->modle == KEYWORD2I_ALL_SC_ERR) || (from->modle == KEYWORD2F_ALL_SC_ERR) || (from->modle == KEYWORD2_STR_ALL_SC_ERR))
            {
                if ((strstr(line, from->err_word) != NULL) && (from->err_word[0] != 0))
                {
                    strcpy(data->data_str, from->err_word);
                    return -1;
                }
            }

            if ((strstr(line, from->key_word) != NULL) || (strstr(from->key_word, "null") != NULL))
            {
                if (strstr(line, "[TX]:") != NULL)
                    continue;
                if (strstr(line, "\e[") != NULL)
                    remove_str_colors(line);
                if ((strstr(line, from->err_word) != NULL) && (from->err_word[0] != 0))
                {
                    strcpy(data->data_str, from->err_word);
                    return -1;
                }
                if ((strstr(line, from->not_word) != NULL) && (from->not_word[0] != 0))
                {
                    continue;
                }

                switch (from->modle)
                {
                case ALL:
                {
                    data->type = DATA_STR;
                    if ((point + strlen(line)) < MAX_DATA_STR_LEN)
                    {
                        memcpy(&data->data_str[point], line, strlen(line));
                        point += strlen(line);
                        continue;
                    }
                    else
                    {
                        memcpy(&data->data_str[point], line, (MAX_DATA_STR_LEN - point));
                        {
                            return 1;
                        }
                    }
                }
                break;
                case ONE_ROW:
                {
                    len = strlen(line);
                    if (len > MAX_DATA_STR_LEN)
                    {
                        len = MAX_DATA_STR_LEN;
                    }
                    strncpy(data->data_str, line, len);
                    data->type = DATA_STR;
                    return 1;
                }
                break;
                case STR2I:
                case STR2F:
                case STRS:
                {
                    if (0 == x)
                    {
                        memset(data_form, 0, sizeof(data_form));
                        sscanf(line, "%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s",
                               data_form[0], data_form[1], data_form[2], data_form[3], data_form[4], data_form[5], data_form[6], data_form[7], data_form[8], data_form[9], data_form[10], data_form[11], data_form[12], data_form[13], data_form[14]);
                        // printf("first d1=%s,d2=%s,d3=%s,d4=%s,d5=%s,d6=%s,d7=%s,d8=%s,d9=%s,d10=%s,d11=%s,d12=%s,d13=%s,d14=%s,d15=%s\n", data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11], data[12], data[13], data[14]);
                        need_formate = 1;
                        break;
                    }
                    else
                        wait_x = 1;
                }
                break;
                case KEYWORD2I:
                case KEYWORD2F:
                case KEYWORD2I_ALL_SC_ERR:
                case KEYWORD2F_ALL_SC_ERR:
                {
                    p1 = strstr(line, from->key_word2);
                    if (p1 != NULL)
                    {
                        p1 += strlen(from->key_word2);
                        sscanf(p1, "%s %s", data_form[0], data_form[1]);
                        // printf("line=%s,key_word2=%s,data_form[0]=%s,data_form[1]=%s\n", line, from->key_word2, data_form[0], data_form[1]);
                        y = 0;
                        need_formate = 1;
                    }
                }
                break;
                case KEYWORD2_STR:
                case KEYWORD2_STR_ALL_SC_ERR:
                {
                    p1 = strstr(line, from->key_word2);
                    if (p1 != NULL)
                    {
                        p1 += strlen(from->key_word2);
                        sscanf(p1, "%[^,;]s", data_form[0]);
                        y = 0;
                        need_formate = 1;
                    }
                }
                break;
                default:
                    break;
                }
            }
            if (need_formate)
                break;
            if (wait_x)
            {
                if (0 != x)
                {
                    x--;
                }
                else
                {
                    memset(data_form, 0, sizeof(data_form));
                    sscanf(line, "%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s",
                           data_form[0], data_form[1], data_form[2], data_form[3], data_form[4], data_form[5], data_form[6], data_form[7], data_form[8], data_form[9], data_form[10], data_form[11], data_form[12], data_form[13], data_form[14]);
                    // printf("second d1=%s,d2=%s,d3=%s,d4=%s,d5=%s,d6=%s,d7=%s,d8=%s,d9=%s,d10=%s,d11=%s,d12=%s,d13=%s,d14=%s,d15=%s\n", data[0], data[1], data[2], data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11], data[12], data[13], data[14]);
                    need_formate = 1;
                    break;
                }
            }
        }
    }
    if (need_formate)
    {
        if (0 != data_form[y][0])
        {
            if (from->modle == STR2I)
            {
                data->type = DATA_INT;
                data->data_int = atoi(data_form[y]);
            }
            else if (from->modle == STR2F)
            {
                data->type = DATA_FLOAT;
                data->data_float = atof(data_form[y]);
            }
            else if (from->modle == STRS)
            {
                data->type = DATA_STR;
                strcpy(data->data_str, data_form[y]);
            }
            else if ((from->modle == KEYWORD2I) || (from->modle == KEYWORD2I_ALL_SC_ERR))
            {
                data->type = DATA_INT;
                data->data_int = atoi(data_form[0]);
            }
            else if ((from->modle == KEYWORD2F) || (from->modle == KEYWORD2F_ALL_SC_ERR))
            {
                data->type = DATA_FLOAT;
                data->data_float = atof(data_form[0]);
            }
            else if ((from->modle == KEYWORD2_STR) || (from->modle == KEYWORD2_STR_ALL_SC_ERR))
            {
                data->type = DATA_STR;
                strcpy(data->data_str, data_form[0]);
            }
            return 1;
        }
    }
    if ((from->modle == ALL) && (point > 0))
    {
        return 1;
    }
    return 0;
}
/*
 * @description        : 获取被测设备IP是否正确
 * @return	           : 无
 */
void func_get_dev_ip(void)
{
    char temp_line[256] = {0};

    bsp_sync_read_point();
    sprintf(temp_line, "ifconfig %s", test->net_dev);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    if (bsp_check_keyword("*************", READ_POINT_SYNC))
    {
        info.tester_ip_ok = TESTER_IP_OK;
        printf("tester ip is ok\n");
    }
    else
    {
        info.tester_ip_ok = TESTER_IP_ERR;
        printf("tester ip is not *************\n");
    }
    return;
}
/*
 * @description    : 查询设备当前状态,判别依据，连续2次回车获得结果一致
 * @param - start_info   : 起机信息
 * @param - con    : 控制台相关信息
 * @param - timeout : 超时时间，单位ms
 * @return		   : 系统当前状态，详见enum_sys_status
 */
enum_sys_status func_get_sys_status(struct_sys_start *start_info, struct_console *con, int timeout)
{
    int count[MAX_SYS_STATUS] = {0};
    unsigned long start_time = 0, end_time = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            printf("%s err!!!\n", __func__);
            return STA_UNKNOW;
        }

        bsp_sync_read_point();
        send_buff[0] = 0x0d;
        // func_send_frame_nr(tty_fd, send_buff, 1);
        func_send_frame(tty_fd, send_buff, 1);
        usleep(500000);
        // 系统已登录
        if (bsp_check_keyword(start_info->login_str, READ_POINT_NO_SYNC))
        {
            count[SYS_READY]++;
            if (count[SYS_READY] >= 2)
                return SYS_READY;
            else
                continue;
        }
        else
            count[SYS_READY] = 0;
        // 未登录
        if (bsp_check_keyword(start_info->enter_key, READ_POINT_NO_SYNC))
        {
            count[LOG_OUT]++;
            if (count[LOG_OUT] >= 2)
                return LOG_OUT;
            else
                continue;
        }
        else
            count[LOG_OUT] = 0;
        // 等待输入密码
        if (bsp_check_keyword(start_info->pw_key, READ_POINT_NO_SYNC))
        {
            count[WAIT_PW]++;
            if (count[WAIT_PW] >= 2)
                return WAIT_PW;
            else
                continue;
        }
        else
            count[WAIT_PW] = 0;
        // 控制台已准备好
        if (bsp_check_keyword(con->step[1].key, READ_POINT_NO_SYNC))
        {
            count[CONSOLE_READY]++;
            if (count[CONSOLE_READY] >= 2)
                return CONSOLE_READY;
            else
                continue;
        }
        else
            count[CONSOLE_READY] = 0;
        // 控制台菜单
        if (bsp_check_keyword(con->step[0].key, READ_POINT_NO_SYNC))
        {
            count[CONSOLE_MENU]++;
            if (count[CONSOLE_MENU] >= 2)
                return CONSOLE_MENU;
            else
                continue;
        }
        else
            count[CONSOLE_MENU] = 0;
        // 系统GDB交互
        if (bsp_check_keyword("gdb", READ_POINT_NO_SYNC))
        {
            count[SYS_GDB]++;
            if (count[SYS_GDB] >= 2)
                return SYS_GDB;
            else
                continue;
        }
        else
            count[SYS_GDB] = 0;
        send_buff[0] = 0x03;
        func_send_frame(tty_fd, send_buff, 1);
    }
    return STA_UNKNOW;
}
/*
 * @description    : 确认被测设备系统正常起机
 * @param - start_info   : 判别信息
 * @param - timeout : 超时时间，单位ms
 * @return		   : 0-正常 -1 - 异常
 */
int func_makesure_sys_start(struct_sys_start *start_info, int timeout)
{
    int count = 0;
    unsigned long start_time = 0, end_time = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            // printf("%s err!!!\n", __func__);
            print_debug = 0;
            return -1;
        }
        send_buff[0] = 0x0d;
        func_send_frame_nr(tty_fd, send_buff, 1);
        usleep(500000);
        if (bsp_check_keyword(start_info->enter_key, READ_POINT_SYNC))
        {
            count++;
            if (count >= 3)
            {

                return 0;
            }
            else
                continue;
        }
        else
        {
            count = 0;
        }
    }
    return -1;
}
/*
 * @description    : 将本地时间同步到被测设备
 * @param - *str   : 存放时间字符串
 * @return		   : 无
 */
void func_get_local_time_to_str(char *str)
{
    time_t now = time(NULL);
    struct tm *tm = localtime(&now);
    sprintf(str, "date -s \"%d-%02d-%02d %02d:%02d:%02d\"", tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday, tm->tm_hour, tm->tm_min, tm->tm_sec);
}
/*
 * @description    : 初始化被测设备
 * @return		   : 无
 */
void func_init_tester(void)
{
    char temp_line[256] = {0};
    if (test->sys_start.close_echo[0] != 0)
    {
        func_send_frame_nr(tty_fd, test->sys_start.close_echo, strlen(test->sys_start.close_echo));
        char send_buff = 0x0d;
        func_send_frame(tty_fd, &send_buff, 1);
        usleep(50000);
    }
    // 检查被测网卡IP
    if (TESTER_IP_NOT_CHECK == info.tester_ip_ok)
        func_get_dev_ip();

    // 配置被测网络
    sprintf(temp_line, "ifconfig %s up", test->net_dev);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(50000);
    memset(temp_line, 0, sizeof(temp_line));
    sprintf(temp_line, "ifconfig %s %s netmask %s", test->net_dev, "*************", "*************");
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(50000);
    // 获取本机时间，然后通过date命令同步到被测设备
    memset(temp_line, 0, sizeof(temp_line));
    func_get_local_time_to_str(temp_line);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(50000);

    bsp_sync_read_point();
}

/*
 * @description    : 确认登录系统
 * @param - start_info   : 判别信息
 * @return		   : 0-正常 -1 - 异常
 */
int func_login_sys(struct_sys_start *start_info)
{
    send_buff[0] = 0x0d;
    bsp_sync_read_point();
    if (0 == start_info->user_name[0])
    {
        printf("no user name !!!\n");
        if (1 == start_info->need_init_tester)
        {
            func_init_tester();
            start_info->need_init_tester = 0;
        }
        return 0;
    }
    for (int retry = 0; retry < 5; retry++)
    {
        func_send_frame_nr(tty_fd, start_info->user_name, strlen(start_info->user_name));
        // printf("send user name = %s\n", start_info->user_name);
        sleep(1);
        // func_send_frame_nr(tty_fd, send_buff, 1);
        // func_send_frame_nr(tty_fd, send_buff, 1);
        // sleep(1);
        if (bsp_check_keyword(start_info->login_str, READ_POINT_FIND_KW_SYNC))
        {
            if (1 == start_info->need_init_tester)
            {
                func_init_tester();
                start_info->need_init_tester = 0;
            }
            return 0;
        }
        else if ((bsp_check_keyword(start_info->pw_key, READ_POINT_FIND_KW_SYNC)) && (0 != start_info->pw_key[0]))
        {
            func_send_frame_nr(tty_fd, start_info->pass_word, strlen(start_info->pass_word));
            sleep(2);
            if (bsp_check_keyword(start_info->login_str, READ_POINT_SYNC))
            {
                if (1 == start_info->need_init_tester)
                {
                    func_init_tester();
                    start_info->need_init_tester = 0;
                }
                return 0;
            }
            else
            {
                printf("pass word is err !!!\n");
                return -1;
            }
        }
        else if (bsp_check_keyword(start_info->enter_key, READ_POINT_SYNC))
        {
            printf("user name is err !!!");
            return -1;
        }
    }
    printf("login failed !!!\n");
    return -1;
}
/*
 * @description      : 设备起机进入控制台
 * @param - *console : 控制台管理信息
 * @param - timeout  : 超时时间，单位ms
 * @return		     : 0-正常 -1-异常
 */
int func_startup_enter_console(struct_console *console, int timeout)
{
    unsigned long start_time = 0, end_time = 0;
    int step_flag = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            printf("%s err!!!\n", __func__);
            return -1;
        }

        if (step_flag)
        {
            func_send_frame(tty_fd, console->step[1].cmd, strlen(console->step[1].cmd));
            print_debug = 0;
            usleep(200000);
            if (bsp_check_keyword(console->step[1].key, READ_POINT_SYNC))
            {
                return 0;
            }
        }
        else
        {
            func_send_frame_nr(tty_fd, console->step[0].cmd, strlen(console->step[0].cmd));
            usleep(200000);
            if (bsp_check_keyword(console->step[0].key, READ_POINT_SYNC))
            {
                if (0 == console->step[1].cmd[0])
                    return 0;
                else
                {
                    step_flag = 1;
                }
            }
        }
        usleep(10000);
    }
}
/*
 * @description      : 进入控制台
 * @param - *start_info    : 启动判别信息
 * @param - *console : 控制台管理信息
 * @param - timeout  : 超时时间，单位ms
 * @return		     : 0-正常 -1-异常
 */
int func_sys_enter_console(struct_sys_start *start_info, struct_console *console, int timeout)
{
    unsigned long start_time = 0, end_time = 0;
    int first_str_flag = 0, step_flag = 0;

    bsp_sync_read_point();
    func_send_frame_nr(tty_fd, "reboot", strlen("reboot"));
    test->sys_start.need_init_tester = 1;

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            printf("%s err!!!\n", __func__);
            return -1;
        }

        if (first_str_flag)
        {
            if (step_flag)
            {
                func_send_frame(tty_fd, console->step[1].cmd, strlen(console->step[1].cmd));
                print_debug = 0;
                // sleep(1);
                usleep(200000);
                if (bsp_check_keyword(console->step[1].key, READ_POINT_SYNC))
                {
                    return 0;
                }
            }
            else
            {
                // func_send_frame_nr(tty_fd, console->step[0].cmd, strlen(console->step[0].cmd));
                func_send_frame(tty_fd, console->step[0].cmd, strlen(console->step[0].cmd));
                usleep(200000);
                if (bsp_check_keyword(console->step[0].key, READ_POINT_SYNC))
                {
                    if (0 == console->step[1].cmd[0])
                        return 0;
                    else
                    {
                        step_flag = 1;
                    }
                }
            }
        }
        else
        {
            if (bsp_check_keyword(start_info->first_strs[0], READ_POINT_NO_SYNC))
            {
                first_str_flag = 1;
            }
            if (0 != start_info->first_strs[1][0])
            {
                if (bsp_check_keyword(start_info->first_strs[1], READ_POINT_SYNC))
                {
                    first_str_flag = 1;
                }
            }
            if ((0 == first_str_flag) && (end_time - start_time > (20 * 1000)))
            {
                printf("no find first_strs\n");
                return -1;
            }
        }
        usleep(10000);
    }
}
/*
 * @description      : 从控制台目录进入控制台
 * @param - *console : 控制台管理信息
 * @param - timeout  : 超时时间，单位ms
 * @return		     : 0-正常 -1-异常
 */
int func_menu_sys_enter_console(struct_console *console, int timeout)
{
    unsigned long start_time = 0, end_time = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            printf("%s err", __func__);
            return -1;
        }

        func_send_frame(tty_fd, console->step[1].cmd, strlen(console->step[1].cmd));
        print_debug = 0;
        sleep(1);
        if (bsp_check_keyword(console->step[1].key, READ_POINT_SYNC))
        {
            return 0;
        }
    }
}
/*
 * @description      : 退出控制台
 * @param - *start_info    : 启动判别信息
 * @param - *console : 控制台管理信息
 * @param - timeout  : 超时时间，单位ms
 * @return		     : 0-正常 -1-异常
 */
int func_exit_console(struct_sys_start *start_info, struct_console *console, int timeout)
{
    unsigned long start_time = 0, end_time = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            print_debug = 0;
            printf("%s err!!!\n", __func__);
            return -1;
        }

        func_send_frame_nr(tty_fd, console->exit.cmd, strlen(console->exit.cmd));
        sleep(1);
        if (bsp_check_keyword(start_info->first_strs[0], READ_POINT_NO_SYNC))
        {
            print_debug = 0;
            return 0;
        }
        if (0 != start_info->first_strs[1][0])
        {
            if (bsp_check_keyword(start_info->first_strs[1], READ_POINT_SYNC))
            {
                print_debug = 0;
                return 0;
            }
        }
    }
}
/*
 * @description      : 退出gdb
 * @param - *start_info    : 启动判别信息
 * @param - timeout  : 超时时间，单位ms
 * @return		     : 0-正常 -1-异常
 */
int func_exit_gdb(struct_sys_start *start_info, int timeout)
{
    unsigned long start_time = 0, end_time = 0;

    bsp_sync_read_point();

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
        {
            print_debug = 0;
            printf("%s err!!!\n", __func__);
            return -1;
        }

        func_send_frame_nr(tty_fd, "quit", strlen("quit"));
        sleep(1);
        if (bsp_check_keyword(start_info->login_str, READ_POINT_SYNC))
            return 0;
    }
}
/*
 * @description    : 测试项期望系统所在状态
 * @param - start_info   : 起机信息
 * @param - con    : 控制台相关信息
 * @param - timeout: 超时时间，单位ms
 * @return		   : 0-正常 -1 - 异常
 */
int func_set_sys_status(enum_sys_status wish, struct_sys_start *start_info, struct_console *con, int timeout)
{
    enum_sys_status curr_status = 0;

    curr_status = func_get_sys_status(start_info, con, 100000);
    printf("curr_status=%d,wish=%d\n", curr_status, wish);

    if (0 == curr_status)
        return -1;
    else if (wish == curr_status)
    {
        if ((curr_status == SYS_READY) && (start_info->need_init_tester))
        {
            func_init_tester();
            start_info->need_init_tester = 0;
        }
        return 0;
    }

    if (SYS_READY == wish)
    {
        switch (curr_status)
        {
        case CONSOLE_MENU:
            if (-1 == func_menu_sys_enter_console(con, 5000))
                return -1;
            if (-1 == func_exit_console(start_info, con, 100000))
                return -1;
            if (-1 == func_makesure_sys_start(start_info, 100000))
                return -1;
            if (-1 == func_login_sys(start_info))
                return -1;
            break;
        case CONSOLE_READY:
            if (-1 == func_exit_console(start_info, con, 100000))
                return -1;
            if (-1 == func_makesure_sys_start(start_info, 100000))
                return -1;
            if (-1 == func_login_sys(start_info))
                return -1;
            break;
        case LOG_OUT:
            if (-1 == func_login_sys(start_info))
                return -1;
            break;
        case WAIT_PW:
            if (0 == start_info->pass_word[0])
                sleep(5);
            if (-1 == func_login_sys(start_info))
                return -1;
            break;
        case SYS_GDB:
            if (-1 == func_exit_gdb(start_info, 5000))
                return -1;
            break;
        default:
            return -1;
        }
    }
    else if (CONSOLE_READY == wish)
    {
        switch (curr_status)
        {
        case CONSOLE_MENU:
            if (-1 == func_menu_sys_enter_console(con, 5000))
                return -1;
            break;
        case LOG_OUT:
            if (-1 == func_login_sys(start_info))
                return -1;
            if (-1 == func_sys_enter_console(start_info, con, 100000))
                return -1;
            break;
        case WAIT_PW:
            if (0 == start_info->pass_word[0])
                sleep(5);
            if (-1 == func_login_sys(start_info))
                return -1;
            if (-1 == func_sys_enter_console(start_info, con, 100000))
                return -1;
            break;
        case SYS_READY:
            if (-1 == func_sys_enter_console(start_info, con, 100000))
                return -1;
            break;
        case SYS_GDB:
            if (-1 == func_exit_gdb(start_info, 5000))
                return -1;
            if (-1 == func_sys_enter_console(start_info, con, 100000))
                return -1;
            break;
        default:
            return -1;
        }
    }
    return 0;
}
/*
 * @description     : 计算找到指定字符串出现的时差
 * @param - *str1   : 字符串1
 * @param - timeout : 超时时间，单位ms
 * @return		    : 时差，单位ms,-1 - 未找到2个字符
 */
int func_get_one_str_time_diff(char *str1, int timeout)
{
    unsigned long start_time = 0, end_time = 0;
    int diff = 0;

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
            return -1;
        if (bsp_check_keyword(str1, READ_POINT_SYNC))
        {
            diff = (end_time - start_time);
            return diff;
        }
        usleep(1000);
    }
}
/*
 * @description     : 计算2个指定字符串出现的时差
 * @param - *str1   : 字符串1
 * @param - *str2   : 字符串2
 * @param - timeout : 超时时间，单位ms
 * @return		    : 时差，单位ms,-1 - 未找到2个字符
 */
int func_get_two_str_time_diff(char *str1, char *str2, int timeout)
{
    unsigned long start_time = 0, end_time = 0, kw1_time = 0;
    int kw1_flag = 0;

    start_time = func_get_system_time_ms();
    end_time = start_time;
    while (1)
    {
        end_time = func_get_system_time_ms();
        if (end_time - start_time > timeout)
            return -1;
        if (bsp_check_keyword(str1, READ_POINT_FIND_KW_CURRENT))
        {
            kw1_flag = 1;
            kw1_time = func_get_system_time_ms();
        }
        if (bsp_check_keyword(str2, READ_POINT_SYNC))
        {
            if (kw1_flag)
                return (end_time - kw1_time);
        }
        usleep(1000);
    }
}
/*
 * @description           : 将结果或数据存放到帧队列。
 * @param - *item         : item测试结构体
 * @return		          : 无
 */
void func_res_data_to_frame_queue(char *contest, int flag)
{
    int total_len = strlen(contest);
    int frame_size = MAX_SEND_LEN;                               // 每帧最大字节数
    int frame_count = (total_len + frame_size - 1) / frame_size; // 向上取整计算总帧数
    int i, current_len;
    char frame_buffer[1025] = {0}; // 多一个字节存放结束符
    int frame_type = 0;

    if (RES_TO_QUENE == flag)
        frame_type = NET_SEND_RES;
    if (DATA_TO_QUENE == flag)
        frame_type = NET_SEND_DATA;

    // 数据长度小于1024，不需要分帧
    if (total_len <= frame_size)
    {
        strcpy(frame_queue->frame[frame_queue->write_point].data, contest);
        frame_queue->frame[frame_queue->write_point].flag = frame_type;
        frame_queue->frame[frame_queue->write_point].frame_flag = 0; // 不分帧
        frame_queue->frame[frame_queue->write_point].frame_num = 0;
        frame_queue->write_point++;
        frame_queue->write_point %= MAX_SEND_QUEUE_LEN;
        return;
    }

    // 数据长度大于1024，需要分帧
    for (i = 0; i < frame_count; i++)
    {
        // 计算当前帧的数据长度
        current_len = (i == frame_count - 1) ? (total_len - i * frame_size) : frame_size;

        // 复制当前帧的数据
        memset(frame_buffer, 0, sizeof(frame_buffer));
        memcpy(frame_buffer, contest + i * frame_size, current_len);

        // 设置帧标志
        strcpy(frame_queue->frame[frame_queue->write_point].data, frame_buffer);
        frame_queue->frame[frame_queue->write_point].flag = frame_type;

        if (i < frame_count - 1)
        {
            // 不是最后一帧
            frame_queue->frame[frame_queue->write_point].frame_flag = 1; // 分帧且后续还有帧
        }
        else
        {
            // 最后一帧
            frame_queue->frame[frame_queue->write_point].frame_flag = 2; // 分帧且为最后一帧
        }

        frame_queue->frame[frame_queue->write_point].frame_num = (i + 1); // 帧序号

        // 更新写指针
        frame_queue->write_point++;
        frame_queue->write_point %= MAX_SEND_QUEUE_LEN;
    }
}
/*
 * @description       : 整理测试结果
 * @param - *test_item: 一条测试检查
 * @return		      : 无
 */
void func_format_res(struct_test_item *test_item)
{
    int i = 0;
    char check[10] = {0};
    char res[MAX_RES_REASON_LEN * 2] = {0};
    int err_flag = 0;
    void *send_res = calloc(1, MAX_SEND_LEN);
    char data[30] = {0};

    if (NULL == send_res)
    {
        printf("calloc failed  !!!!!!!\n");
        return;
    }

    if (test_item->res_formatting[0] == 0)
        return;
    strcpy(send_res, test_item->res_formatting);
    if (strstr(send_res, "@dur_time") != NULL)
    {
        unsigned long cur_time = func_get_system_time_ms();
        int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;
        sprintf(data, "%d", dur_time);
        replace_substring(send_res, "@dur_time", data);
    }

    for (i = 0; i < VALUE_NUMB; i++)
    {
        memset(check, 0, sizeof(check));
        sprintf(check, "@res%d_", (i + 1));
        if (strstr(send_res, check) != NULL)
        {
            memset(res, 0, sizeof(res));
            if (strstr(test_item->res[i].res, "failed") != NULL)
            {
                sprintf(res, "%s,%s", test_item->res[i].res, test_item->res[i].res_reason);
                // printf("res=%s\n", res);
                replace_substring(send_res, check, res);
                err_flag = 1;
            }
            else if (strstr(test_item->res[i].res, "passed") != NULL)
            {
                sprintf(res, "%s,%s", test_item->res[i].res, " ");
                // printf("res=%s\n", res);
                replace_substring(send_res, check, res);
            }
        }
    }

    func_res_data_to_frame_queue(send_res, RES_TO_QUENE);
    if (err_flag)
        bsp_print_save("ERR", send_res);
    else
        bsp_print_save("OK", send_res);

    free(send_res);
    return;
}
/*
 * @description       : 整理测试数据
 * @param - *test_item: 一条测试检查
 * @return		      : 无
 */
void func_format_data(struct_test_item *test_item)
{
    int i = 0;
    char check[10] = {0};
    char data[300] = {0};
    void *send_data = calloc(1, MAX_SEND_LEN);

    if (NULL == send_data)
    {
        printf("calloc failed  !!!!!!!\n");
        return;
    }

    if (test_item->data_formatting[0] == 0)
        return;
    strcpy(send_data, test_item->data_formatting);
    if (strstr(send_data, "@dur_time") != NULL)
    {
        unsigned long cur_time = func_get_system_time_ms();
        int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;
        sprintf(data, "%d", dur_time);
        replace_substring(send_data, "@dur_time", data);
    }

    for (i = 0; i < VALUE_NUMB; i++)
    {
        memset(check, 0, sizeof(check));
        sprintf(check, "@data%d_", (i + 1));
        if (strstr(send_data, check) != NULL)
        {
            memset(data, 0, sizeof(data));
            if (test_item->data[i].type == DATA_INT)
            {
                sprintf(data, "%d,%d", DATA_INT, test_item->data[i].data_int);
                // printf("data=%s\n", data);
                replace_substring(send_data, check, data);
            }
            if (test_item->data[i].type == DATA_FLOAT)
            {
                sprintf(data, "%d,%0.3f", DATA_FLOAT, test_item->data[i].data_float);
                // printf("data=%s\n", data);
                replace_substring(send_data, check, data);
            }
            if (test_item->data[i].type == DATA_STR)
            {
                sprintf(data, "%d,%s", DATA_STR, test_item->data[i].data_str);
                // printf("data=%s\n", data);
                replace_substring(send_data, check, data);
            }
        }
    }
    func_res_data_to_frame_queue(send_data, DATA_TO_QUENE);
    bsp_print_save("DATA", send_data);
    free(send_data);
    return;
}
/*
 * @description            : 被测1条命令结果检查，
 * @param - *test_item     : 一条测试检查
 * @param - group          : 第几组
 * @param - line           : 第几行
 * @return		           : 0-没找到 -1-异常 1-正常
 */
int func_line_check(struct_test_item *test_item, int group, int line)
{
    int i = 0, point = 0;
    int result = 0, back_res = 0;
    int get_data_flag = 1, get_res_flag = 1;
    struct_data_class data;
    void *send_data = calloc(1, 512);
    int have_res_count = 0;
    int have_failed = 0;

    // 检查指针是否有效
    if (test_item == NULL || group < 0 || group >= MAX_GROUP ||
        line < 0 || line >= test_item->group[group].line_count ||
        test_item->group[group].line == NULL)
    {
        if (send_data)
            free(send_data);
        return -1;
    }

    if (NULL == send_data)
    {
        printf("calloc failed  !!!!!!!\n");
        return -1;
    }
    unsigned long cur_time = func_get_system_time_ms();
    int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;
    pthread_mutex_lock(&mutex);
    // 查找正确或错无关键字，注意3种类型的关键字
    if (test_item->group[group].line[line].res_count)
    {
        have_res_count = 1;
        for (i = 0; i < test_item->group[group].line[line].res_count; i++)
        {
            result = bsp_check_mul_keyword(pLogFile, read_point, &test_item->group[group].line[line].get_res[i]);
            if (result == 1)
            {
                point = atoi(&test_item->group[group].line[line].get_res[i].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "passed");
                // printf("test_item->res[%d].res=%s\n", point, test_item->res[point].res);
                if (test_item->group[group].line[line].get_res[i].modle == ALL_SCAN)
                {
                    break;
                }
            }
            else if (result == -1)
            {
                point = atoi(&test_item->group[group].line[line].get_res[i].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "failed");
                // printf("test_item->res[%d].res=%s\n", point, test_item->res[point].res);
                // back_res = -1;
                test_item->term_test = 1;
                have_failed = 1;
            }
            else
            {
                get_res_flag = 0;
            }
        }
    }
    else
    {
        get_res_flag = 2;
    }
    if (1 == have_failed)
    {
        if (test_item->group[group].line[line].res_reason_count)
        {
            for (i = 0; i < test_item->group[group].line[line].res_reason_count; i++)
            {
                memset(&data, 0, sizeof(struct_data_class));
                result = bsp_get_value(pLogFile, read_point, &test_item->group[group].line[line].get_res_reason[i], &data);
                if (result == 1)
                {
                    point = atoi(&test_item->group[group].line[line].get_res_reason[i].storage[11]);
                    if (point > 0)
                        point--;
                    strcpy(test_item->res[point].res_reason, data.data_str);
                    // printf("test_item->res[%d].res_reason=%s\n", point, test_item->res[point].res_reason);
                }
            }
        }
    }
    // 判别获取数据个数，获取结果个数
    if (test_item->group[group].line[line].data_count)
    {
        for (i = 0; i < test_item->group[group].line[line].data_count; i++)
        {
            // printf("data_count=%d,get_data[%d].key_word=%s\n", test_item->group[group].line[line].data_count, i, test_item->group[group].line[line].get_data[i].key_word);
            memset(&data, 0, sizeof(struct_data_class));
            result = bsp_get_value(pLogFile, read_point, &test_item->group[group].line[line].get_data[i], &data);
            // printf("result=%d,data.data_int=%d\n", result, data.data_int);
            if (result == 1)
            {
                point = atoi(&test_item->group[group].line[line].get_data[i].storage[5]);
                if (point > 0)
                    point--;
                memcpy(&test_item->data[point], &data, sizeof(struct_data_class));
            }
            else if (result == -1)
            {
                if ((0 == have_res_count) && (back_res != -1))
                {
                    sprintf(send_data, "%s:%d;1;Tester cmd %s,failed,have err keyword %s;\n", test_item->title, dur_time, test_item->group[group].line[line].content, data.data_str);
                    bsp_print_save("ERR", send_data);
                    back_res = -1;
                    test_item->term_test = 1;
                    func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                }
                // goto __Tester_back_res;
            }
            else
            {
                // back_res = 0;
                get_data_flag = 0;
            }
        }
    }
    else
    {
        get_data_flag = 2;
    }

__Tester_back_res:
    pthread_mutex_unlock(&mutex);
    free(send_data);
    if (back_res == -1)
        return back_res;
    if ((get_data_flag == 0) || (get_res_flag == 0))
        return 0;
    return 1;
}
/*
 * @description            : 测试工装1条命令结果检查，
 * @param - *test_item     : 一条测试检查
 * @param - group          : 第几组
 * @param - line           : 第几行
 * @return		           : -1-异常 1-正常
 */
int func_tool_line_check(struct_test_item *test_item, int group, int line)
{
    FILE *temp_fp = NULL;
    char file_name[40] = {0};
    int i = 0, point = 0;
    int result = 0, get_data_flag = 1, get_res_flag = 1, back_res = 1;
    struct_data_class data;
    void *send_data = calloc(1, 512);
    int have_res_count = 0;
    int have_failed = 0;

    if (NULL == send_data)
    {
        printf("calloc failed  !!!!!!!\n");
        return -1;
    }
    unsigned long cur_time = func_get_system_time_ms();
    int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;
    // 打开工装保存日志文件
    sprintf(file_name, "/auto/temp/line%d.txt", line);
    temp_fp = file_open(file_name, ONLY_R);
    if (NULL == temp_fp)
    {
        printf("open temp file failed file_name=%s\n", file_name);
        return -1;
    }

    if ((test_item->group[group].line[line].data_count == 0) && (test_item->group[group].line[line].res_count == 0))
    {
        goto __Tool_back_res;
    }
    // 查找正确或错无关键字，注意3种类型的关键字
    if (test_item->group[group].line[line].res_count)
    {
        have_res_count = 1;
        for (i = 0; i < test_item->group[group].line[line].res_count; i++)
        {
            result = bsp_check_mul_keyword(temp_fp, 0, &test_item->group[group].line[line].get_res[i]);
            if (result == 1)
            {
                point = atoi(&test_item->group[group].line[line].get_res[i].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "passed");
                if (test_item->group[group].line[line].get_res[i].modle == ALL_SCAN)
                {
                    break;
                }
            }
            else if (result == -1)
            {
                point = atoi(&test_item->group[group].line[line].get_res[i].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "failed");
                back_res = -1;
                test_item->term_test = 1;
                have_failed = 1;
            }
            else
            {
                get_res_flag = 0;
            }
        }
    }
    else
    {
        get_res_flag = 2;
    }
    if (1 == have_failed)
    {
        if (test_item->group[group].line[line].res_reason_count)
        {
            for (i = 0; i < test_item->group[group].line[line].res_reason_count; i++)
            {
                memset(&data, 0, sizeof(struct_data_class));
                result = bsp_get_value(temp_fp, 0, &test_item->group[group].line[line].get_res_reason[i], &data);
                if (result == 1)
                {
                    point = atoi(&test_item->group[group].line[line].get_res_reason[i].storage[11]);
                    if (point > 0)
                        point--;
                    strcpy(test_item->res[point].res_reason, data.data_str);
                }
            }
        }
    }
    // 判别获取数据个数，获取结果个数
    if (test_item->group[group].line[line].data_count)
    {
        for (i = 0; i < test_item->group[group].line[line].data_count; i++)
        {
            memset(&data, 0, sizeof(struct_data_class));
            result = bsp_get_value(temp_fp, 0, &test_item->group[group].line[line].get_data[i], &data);
            if (result == 1)
            {
                point = atoi(&test_item->group[group].line[line].get_data[i].storage[5]);
                if (point > 0)
                    point--;
                memcpy(&test_item->data[point], &data, sizeof(struct_data_class));
            }
            else if (result == -1)
            {
                if ((0 == have_res_count) && (back_res != -1))
                {
                    sprintf(send_data, "%s:%d;1;Tool cmd %s,failed,have err keyword %s;\n", test_item->title, dur_time, test_item->group[group].line[line].content, data.data_str);
                    bsp_print_save("ERR", send_data);
                    back_res = -1;
                    test_item->term_test = 1;
                    func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                }
                // goto __Tool_back_res;
            }
            else
            {
                // back_res = 0;
                get_data_flag = 0;
            }
        }
    }
    else
    {
        get_data_flag = 2;
    }

__Tool_back_res:
    free(send_data);
    if (temp_fp != NULL)
    {
        FILE *pSelfLog_fp = NULL;
        pSelfLog_fp = file_open(self_log_file, ATWR);
        if (NULL == pSelfLog_fp)
        {
            printf("open self log file failed\n");
            file_close(temp_fp);
            return -1;
        }
        fseek(pSelfLog_fp, 0, SEEK_END);
        fseek(temp_fp, 0, SEEK_SET);
        file_add(pSelfLog_fp, temp_fp);
        file_close(temp_fp);
        file_close(pSelfLog_fp);
        unlink(file_name);
    }

    if (back_res == -1)
        return back_res;
    if ((get_data_flag == 0) || (get_res_flag == 0))
        return 0;
    return 1;
}
/*
 * @description        : 为下一项测试做准备。
 * @param - *test_para : 测试参数
 * @param - model      : 测试模型
 * @return		       : -1 暂停测试 0正常
 */
int func_set_sys_new_status(struct_test *test_para, enum_test_model model)
{
    switch (model)
    {
    case ENTER_CONSOLE_STR:
    case ENTER_CONSOLE_WAIT:
    case SYS_INTER:
    case SYS_WATCHDOG:
        if (-1 == func_set_sys_status(SYS_READY, &(test_para->sys_start), &(test_para->console), 120000))
            return -1;
        break;
    case CONSOLE_INTER:
    case CONSOLE_WATCHDOG:
        if (-1 == func_set_sys_status(CONSOLE_READY, &(test_para->sys_start), &(test_para->console), 120000))
            return -1;
        break;
    default:
        return -1;
    }
    return 0;
}

/*
 * @description       : 所有命令回收。
 * @param - *group    : 测试参数
 * @return		      : 1-正常 0-测试中
 */
void func_recycle_all_cmd(struct_test_group *group)
{
    int i = 0;
    char cmd[256] = {0};
    char temp[300] = {0};

    // 检查指针是否有效
    if (group == NULL || group->line == NULL)
    {
        return;
    }

    for (i = 0; i < group->line_count; i++)
    {
        if (group->line[i].flag == CMD_TOOL)
        {
            if (0 != check_command_pid(group->line[i].pid))
            {
                kill_command_pid(group->line[i].pid);
            }
        }
        else if (group->line[i].flag == CMD_TESTER)
        {
            sscanf(group->line[i].content, "%s", cmd);
            sprintf(temp, "killall %s", cmd);
            func_send_frame_nr(tty_fd, temp, strlen(temp));
        }
        else if (group->line[i].flag == CMD_SPECIAL)
        {
            // 此处什么都不用做，因为测试下一项时会判定系统状态
        }
    }
}

/*
 * @description       : 所有命令回收。
 * @param - *group    : 测试参数
 * @return		      : 1-正常 0-测试中
 */
void func_recycle_skip_end_pid(struct_test_item *test_item)
{
    int i = 0;
    if (test_item->skip_end_pid.pid_num == 0)
        return;
    for (i = 0; i < test_item->skip_end_pid.pid_num; i++)
    {
        if (0 != check_command_pid(test_item->skip_end_pid.pid[i]))
        {
            kill_command_pid(test_item->skip_end_pid.pid[i]);
        }
    }
    test_item->skip_end_pid.pid_num = 0;
}
/*
 * @description       : 特殊命令处理。
 * @param - *line     : 测试参数
 * @param - flag      : 0-执行 1-检查
 * @return		      : 1-正常 0-测试中 -1-异常
 */
int func_special_cmd(struct_test_line *line, enum_test_model test_model, int flag, int human_operation_flag, struct_test_item *test_item)
{
    char *point = NULL;
    int exe_time = 0;

    if (flag == SPECIAL_EXE)
    {
        if ((point = strstr(line->content, "expect_power_down")))
        {
            // 例如expect_power_down 3，把3提出来
            point += strlen("expect_power_down");
            exe_time = atoi(point);
            func_set_gpio_sta(test->tester_power_path, 0);
            sleep(exe_time);
            printf("power down %d s\n", exe_time);
            func_set_gpio_sta(test->tester_power_path, 1);
            test->sys_start.need_init_tester = 1;
            sleep(10);
            return 1; // 正常执行
        }
        else if ((point = strstr(line->content, "expect_reboot")))
        {
            point += strlen("expect_reboot");
            func_send_frame_nr(tty_fd, "reboot", strlen("reboot"));
            test->sys_start.need_init_tester = 1;
            sleep(3);
            return 1; // 正常执行
        }
        else if ((point = strstr(line->content, "expect_human_operation")))
        {
            point += strlen("expect_human_operation");
            strcpy(human_operation, point);
            test_manage_send_event_to_net(NET_SEND_HUM_OPER);
            return 1; // 正常执行
        }
        else if ((point = strstr(line->content, "expect_human_confirm")))
        {
            test_manage_send_event_to_net(NET_SEND_HUM_CONFIRM);
            return 1; // 正常执行
        }
        else if ((strstr(line->content, "check_tester_init_ip")) != NULL)
        {
            return 1; // 正常执行
        }
    }
    else if (flag == SPECIAL_CHECK)
    {
        if ((point = strstr(line->content, "expect_human_operation")))
        {
            if (human_operation_flag == NET_BACK_HUM_OPER_OK)
            {
                printf("human operation ok %s\n", __func__);
                return 1;
            }
            else if (human_operation_flag == NET_BACK_HUM_OPER_NG)
            {
                printf("human operation ng %s\n", __func__);
                int point = 0;
                point = atoi(&line->get_res[0].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "failed");
                strcpy(test_item->res[point].res_reason, "human operation not good");
                test_item->term_test = 1;

                return -1;
            }
            else
            {
                return 0;
            }
        }
        else if ((point = strstr(line->content, "expect_human_confirm")))
        {
            if (human_operation_flag == NET_BACK_HUM_CONFIRM_OK)
            {
                printf("human confirm ok %s\n", __func__);
                int point = 0;
                point = atoi(&line->get_res[0].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "passed");

                return 1;
            }
            else if (human_operation_flag == NET_BACK_HUM_CONFIRM_NG)
            {
                printf("human confirm ng %s\n", __func__);
                int point = 0;
                point = atoi(&line->get_res[0].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "failed");
                strcpy(test_item->res[point].res_reason, "human confirm not good");
                test_item->term_test = 1;

                return -1;
            }
            else
            {
                return 0;
            }
        }
        else if ((strstr(line->content, "check_tester_init_ip")) != NULL)
        {
            int point = 0;
            if (info.tester_ip_ok == TESTER_IP_OK)
            {
                point = atoi(&line->get_res[0].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "passed");

                return 1;
            }
            else
            {
                point = atoi(&line->get_res[0].storage[4]);
                if (point > 0)
                    point--;
                strcpy(test_item->res[point].res, "failed");
                strcpy(test_item->res[point].res_reason, "tester ip is not *************");
                test_item->term_test = 1;

                return -1;
            }
        }
        else
            return 1; // 正常执行
    }
    return -1;
}

/*
 * @description       : 模式0，输入reboot后，开始输乱字符，确认是否正确开机。确认进入控制台字符串。
 * @param - *test_para: 测试参数
 * @param - test_item : 测试哪一项
 * @param - flag      : 0-续测 1-停止测试
 * @return		      : 1-正常测试完成 0-测试中 -1-异常
 */
int func_model_0_test(struct_test *test_para, struct_test_item *test_item, int *flag)
{
    char rand_str[1024] = {0};
    unsigned long current_time;

    if (*flag)
        return -1;

    switch (test_item->group[test_item->group_cur].step)
    {
    case 0:
    {
        test_item->process_time = func_get_system_time_ms();
        func_send_frame_nr(tty_fd, "reboot", strlen("reboot"));
        test->sys_start.need_init_tester = 1;
        usleep(50000);
        test_item->group[test_item->group_cur].step++;
    }
    break;
    case 1:
    {
        current_time = func_get_system_time_ms();
        if (current_time - test_item->process_time > 100000)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "The system timeout does not start normally");
            func_format_res(test_item);
            return -1;
        }
        if (bsp_check_keyword(test_item->param[0].data_str, READ_POINT_NO_KW_SYNC))
        {
            test_item->group[test_item->group_cur].step++;
        }
    }
    break;
    case 2:
    {
        func_get_rand_char(rand_str, 1000);
        func_send_frame(tty_fd, rand_str, 1000);
        sleep(1);
        test_item->group[test_item->group_cur].step++;
    }
    break;
    case 3:
    {
        current_time = func_get_system_time_ms();
        if (current_time - test_item->process_time > 100000)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "The system timeout does not start normally");
            func_format_res(test_item);
            return -1;
        }

        if (0 == func_makesure_sys_start(&(test_para->sys_start), 2000))
        {
            strcpy(test_item->res[0].res, "passed");
            func_format_res(test_item);
            return 1;
        }
        if (bsp_check_keyword(test_para->console.step[0].key, READ_POINT_NO_SYNC))
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "The system enter console");
            func_format_res(test_item);
            return -1;
        }
        if (0 != test_para->console.step[1].key[0])
        {
            if (bsp_check_keyword(test_para->console.step[1].key, READ_POINT_SYNC))
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "The system enter console");
                func_format_res(test_item);
                return -1;
            }
        }
    }
    break;
    default:
        return 1;
    }
    usleep(10000);
    return 0;
}
/*
 * @description       : 模式1，输入reboot后，计算收到2个指定字符之间的时间差。
 * @param - *test_para: 测试参数
 * @param - test_item : 测试哪一项
 * @param - flag      : 0-续测 1-停止测试
 * @return		      : 1-正常测试完成 0-测试中 -1-异常
 */
int func_model_1_test(struct_test *test_para, struct_test_item *test_item, int *flag)
{
    int value = 0;
    char temp_char[100] = {0};

    func_send_frame_nr(tty_fd, "reboot", strlen("reboot"));
    test->sys_start.need_init_tester = 1;
    usleep(10000);

    value = func_get_two_str_time_diff(test_item->param[0].data_str, test_item->param[1].data_str, 30000);
    if (-1 == value)
    {
        strcpy(test_item->res[0].res, "failed");
        strcpy(test_item->res[0].res_reason, "The keyword was not found");
        func_format_res(test_item);
        return -1;
    }
    else
    {
        sprintf(temp_char, "Enter the console waiting time is %d\n", value);
        if ((value < (test_item->param[1].data_int + 400)) && (value > (test_item->param[1].data_int - 400)))
        {
            strcpy(test_item->res[0].res, "passed");
            func_format_res(test_item);
            return 1;
        }
        else
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "The time is not correct");
            func_format_res(test_item);
            return -1;
        }
    }

    return 1;
}
/*
 * @description       : 模式3，命令交互。
 * @param - *test_para: 测试参数
 * @param - test_item : 测试哪一项
 * @param - flag      : 0-续测 1-停止测试
 * @param - human_operation_flag: 0-无 1-人机交互确认 2-人机交互不确认
 * @return		      : 1-正常测试完成 0-测试中 -1-异常
 */
int func_model_3_test(struct_test *test_para, struct_test_item *test_item, int *flag, int human_operation_flag)
{
    int result = 0;
    int i = 0;
    char temp_buff[255] = {0};
    int test_completed = 0;

    if (*flag == STOP_TEST)
    {
        func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
        // 此处仅执行回收组，不保证正确执行完，因为此时测试已经停止，不再执行回收组，所以不用等待命令执行完成
        if ((test_item->group_recover != 0) && (test_item->group_recover != test_item->group_cur))
        {
            for (i = 0; i < test_item->group[test_item->group_recover].line_count; i++)
            {
                bsp_line_replace_var(test_item, test_item->group_recover, i);
                if (test_item->group[test_item->group_recover].line[i].flag == CMD_TOOL)
                {
                    memset(temp_buff, 0, sizeof(temp_buff));
                    sprintf(temp_buff, "%s > /auto/temp/line%d.txt 2>&1 &", test_item->group[test_item->group_recover].line[i].content, i);
                    bsp_my_system(temp_buff, &test_item->group[test_item->group_recover].line[i].pid);
                    if (0 == check_command_pid(test_item->group[test_item->group_recover].line[i].pid))
                    {
                        printf("Tool cmd %s,execute end;\n", test_item->group[test_item->group_recover].line[i].content);
                    }
                }
                else if (test_item->group[test_item->group_recover].line[i].flag == CMD_TESTER)
                {
                    printf("Tester cmd %s,execute start;\n", test_item->group[test_item->group_recover].line[i].content);
                    func_send_frame_nr(tty_fd, test_item->group[test_item->group_recover].line[i].content, strlen(test_item->group[test_item->group_recover].line[i].content));
                }
                else if (test_item->group[test_item->group_recover].line[i].flag == CMD_SPECIAL)
                {
                    func_special_cmd(&(test_item->group[test_item->group_recover].line[i]), test_item->modle, SPECIAL_EXE, human_operation_flag, test_item);
                }
            }
            usleep(400000); // 等待命令执行完成
        }
        return -1;
    }
    else if (*flag == WAIT_RECOVER_TEST)
    {
        if (test_item->group_recover != 0)
        {
            if (test_item->group_recover != test_item->group_cur)
            {
                func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
                test_item->group_cur = test_item->group_recover;
                test_item->group[test_item->group_cur].step = 0;
            }
        }
        else
        {
            func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
            return -1;
        }
    }

    switch (test_item->group[test_item->group_cur].step)
    {
    case 0:
    {
        for (i = 0; i < test_item->group[test_item->group_cur].line_count; i++)
        {
            if (NULL == test_item->group[test_item->group_cur].line[i].content)
            {
                void *send_data = calloc(1, 512);

                if (NULL == send_data)
                {
                    printf("calloc failed  !!!!!!!\n");
                    return -1;
                }
                unsigned long cur_time = func_get_system_time_ms();
                int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;

                sprintf(send_data, "%s:%d;1;Tool or Tester cmd is empty,failed,cmd is empty;\n", test_item->title, dur_time);
                bsp_print_save("ERR", send_data);
                func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                free(send_data);
                return -1;
            }
            // 将命令里的变量替换
            bsp_line_replace_var(test_item, test_item->group_cur, i);
            test_item->group[test_item->group_cur].line[i].status = 0;
        }
        test_item->group[test_item->group_cur].step++;
    }
    break;
    case 1:
    {
        // 执行命令
        bsp_sync_read_point();
        save_no_time = 1; // 命令交互期间存储的日志不加时间戳，因为会破坏原格式
        for (i = 0; i < test_item->group[test_item->group_cur].line_count; i++)
        {
            if (test_item->group[test_item->group_cur].line[i].flag == CMD_TOOL)
            {
                memset(temp_buff, 0, sizeof(temp_buff));
                sprintf(temp_buff, "%s > /auto/temp/line%d.txt 2>&1 &", test_item->group[test_item->group_cur].line[i].content, i);
                bsp_my_system(temp_buff, &test_item->group[test_item->group_cur].line[i].pid);
                if (0 == check_command_pid(test_item->group[test_item->group_cur].line[i].pid))
                {
                    printf("Tool cmd %s,execute end;\n", test_item->group[test_item->group_cur].line[i].content);
                }
            }
            else if (test_item->group[test_item->group_cur].line[i].flag == CMD_TESTER)
            {
                printf("Tester cmd %s,execute start;\n", test_item->group[test_item->group_cur].line[i].content);
                func_send_frame_nr(tty_fd, test_item->group[test_item->group_cur].line[i].content, strlen(test_item->group[test_item->group_cur].line[i].content));
            }
            else if (test_item->group[test_item->group_cur].line[i].flag == CMD_SPECIAL)
            {
                func_special_cmd(&(test_item->group[test_item->group_cur].line[i]), test_item->modle, SPECIAL_EXE, human_operation_flag, test_item);
            }
        }
        usleep(400000); // 等待命令执行完成
        test_item->group[test_item->group_cur].step++;
    }
    break;
    case 2:
    {
        // 检查结果
        for (i = 0; i < test_item->group[test_item->group_cur].line_count; i++)
        {
            if (test_item->group[test_item->group_cur].line[i].status == 0)
            {
                if (test_item->group[test_item->group_cur].line[i].flag == CMD_TOOL)
                {
                    if (NULL != strstr(test_item->group[test_item->group_cur].line[i].content, "skip_end"))
                    {
                        test_item->skip_end_pid.pid[test_item->skip_end_pid.pid_num++] = test_item->group[test_item->group_cur].line[i].pid;
                        if (test_item->skip_end_pid.pid_num >= MAX_EXE_LINE)
                        {
                            printf("skip_end_pid is full  !!!\n");
                        }
                        test_item->group[test_item->group_cur].line[i].status = 1;
                        continue;
                    }
                    if (0 != check_command_pid(test_item->group[test_item->group_cur].line[i].pid))
                    {
                        continue;
                    }
                    else
                    {
                        if (-1 == func_tool_line_check(test_item, test_item->group_cur, i))
                        {
                            func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
                            // func_recycle_skip_end_pid(test_item);
                            // return -1;
                        }
                        test_item->group[test_item->group_cur].line[i].status = 1;
                    }
                }
                else if (test_item->group[test_item->group_cur].line[i].flag == CMD_TESTER)
                {
                    result = func_line_check(test_item, test_item->group_cur, i);
                    if (1 == result)
                    {
                        test_item->group[test_item->group_cur].line[i].status = 1;
                    }
                    if (-1 == result)
                    {
                        func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
                        // func_recycle_skip_end_pid(test_item);
                        // return -1;
                        test_item->group[test_item->group_cur].line[i].status = 1;
                    }
                }
                else if (test_item->group[test_item->group_cur].line[i].flag == CMD_SPECIAL)
                {
                    result = func_special_cmd(&(test_item->group[test_item->group_cur].line[i]), test_item->modle, SPECIAL_CHECK, human_operation_flag, test_item);
                    if (1 == result)
                    {
                        test_item->group[test_item->group_cur].line[i].status = 1;
                    }
                    if (-1 == result)
                    {
                        func_recycle_all_cmd(&(test_item->group[test_item->group_cur]));
                        // func_recycle_skip_end_pid(test_item);
                        // return -1;
                        test_item->group[test_item->group_cur].line[i].status = 1;
                    }
                }
            }
        }
        test_completed = 1;
        for (i = 0; i < test_item->group[test_item->group_cur].line_count; i++)
        {
            if (test_item->group[test_item->group_cur].line[i].status == 0)
            {
                test_completed = 0;
                break;
            }
        }
        if (test_completed)
        {
            test_item->group[test_item->group_cur].step++;
        }
    }
    break;
    case 3:
    {
        save_no_time = 0;
        // 判断是否需要提前结束测试
        if (test_item->term_test)
        {
            func_recycle_skip_end_pid(test_item);
            if ((test_item->group_recover != 0) && (test_item->group_recover != test_item->group_cur))
            {
                test_item->group_cur = test_item->group_recover;
                test_item->group[test_item->group_cur].step = 0;
                *flag = WAIT_RECOVER_TEST;
                func_format_res(test_item);
                func_format_data(test_item);
                return 0;
            }
            else
            {
                if (*flag != WAIT_RECOVER_TEST)
                {
                    func_format_res(test_item);
                    func_format_data(test_item);
                }
                test_item->group_cur = 0;
                return -1;
            }
        }
        // 判断是否都测试结束
        test_item->group_cur++;
        if (test_item->group_cur >= test_item->group_count)
        {
            test_item->group_cur = 0;
            if (*flag != WAIT_RECOVER_TEST)
            {
                func_format_res(test_item);
                func_format_data(test_item);
            }
            func_recycle_skip_end_pid(test_item);
            return 1;
        }
    }
    break;
    default:
        break;
    }

    return 0;
}
/*
 * @description       : 模式4，UBOOT阶段看门狗测试。
 * @param - *test_para: 测试参数
 * @param - items     : 测试哪一项
 * @param - groups    : 测试哪一组
 * @return		      : 1-正常 0-测试中 -1-测试异常，无法自恢复，退出测试
 */
int func_model_4_test(struct_test *test_para, struct_test_item *test_item, int *flag)
{
    unsigned long diff_time;
    int i = 0, j = 0;
    int result = -1;
    int wait_time = 0;

    if (*flag)
    {
        return -1;
    }

    switch (test_item->group[0].step)
    {
    case 0:
    {
        for (i = 0; i < test_item->group_count; i++)
        {
            for (j = 0; j < test_item->group[j].line_count; j++)
            {
                if (NULL == test_item->group[i].line[j].content)
                {
                    void *send_data = calloc(1, 512);

                    if (NULL == send_data)
                    {
                        printf("calloc failed  !!!!!!!\n");
                        return -1;
                    }
                    unsigned long cur_time = func_get_system_time_ms();
                    int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;

                    sprintf(send_data, "%s:%d;1;Tester cmd is empty,failed,cmd is empty;\n", test_item->title, dur_time);
                    bsp_print_save("ERR", send_data);
                    func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                    free(send_data);
                    return -1;
                }
                test_item->group[i].line[j].status = 0;
            }
        }
        test_item->group[0].step++;
    }
    break;
    case 1:
    {
        // 执行命令
        bsp_sync_read_point();
        save_no_time = 1; // 命令交互期间存储的日志不加时间戳，因为会破坏原格式
        for (i = 0; i < test_item->group[0].line_count; i++)
        {
            func_send_frame_nr(tty_fd, test_item->group[0].line[i].content, strlen(test_item->group[0].line[i].content));
        }
        test_item->group[0].step++;
    }
    break;
    case 2:
    {
        // 检查结果
        for (i = 0; i < test_item->group[0].line_count; i++)
        {
            result = func_line_check(test_item, 0, i);
            if (1 == result)
            {
                test_item->group[0].line[i].status = 1;
            }
            else
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "The keyword was not found");
                func_format_res(test_item);

                test_item->group[0].step = 9;
            }
        }
        bsp_sync_read_point();
        for (i = 0; i < test_item->group[1].line_count; i++)
        {
            func_send_frame_nr(tty_fd, test_item->group[1].line[i].content, strlen(test_item->group[1].line[i].content));
        }

        test_item->group[0].step++;
    }
    break;
    case 3: // 退出控制台
    {
        result = func_exit_console(&(test_para->sys_start), &(test_para->console), 100000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "can not exit console");
            func_format_res(test_item);

            test_item->group[0].step = 9;
        }
        else
        {
            test_item->group[0].step++;
        }
    }
    break;
    case 4: // 进入控制台
    {
        result = func_startup_enter_console(&(test_para->console), 10000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "can not enter console");
            func_format_res(test_item);
            test_item->group[0].step = 6;
        }
        else
        {
            test_item->process_time = func_get_system_time_ms();
            test_item->group[0].step++;
        }
    }
    break;
    case 5: // 等待重启
    {
        wait_time = test_item->param[0].data_int + 2000;
        result = func_get_one_str_time_diff(test_para->sys_start.first_strs[0], wait_time);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "watchdog not work");
            func_format_res(test_item);

            test_item->group[0].step = 8;
        }
        else
        {
            diff_time = func_get_system_time_ms();
            diff_time = diff_time - test_item->process_time;
            printf("diff_time=%ld\n", diff_time);
            wait_time = test_item->param[0].data_int * 1000;
            if ((diff_time < (wait_time - 1000)) || (diff_time > (wait_time + 1000)))
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "watchdog time err");
                func_format_res(test_item);
            }
            else
            {
                strcpy(test_item->res[0].res, "passed");
                func_format_res(test_item);
            }
            test_item->group[0].step++;
        }
    }
    break;
    case 6: // 进入控制台
    {
        result = func_startup_enter_console(&(test_para->console), 20000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "can not enter console");
            func_format_res(test_item);

            test_item->group[0].step++;
        }
        else
        {
            test_item->group[0].step += 2;
        }
    }
    break;
    case 7: // 进入控制台失败，等待系统起
    {
        result = func_makesure_sys_start(&(test_para->sys_start), 100000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "System status is uncertain");
            func_format_res(test_item);
            save_no_time = 0;
            return -1;
        }
        else
        {
            result = func_login_sys(&(test_para->sys_start));
            if (-1 == result)
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "System status is uncertain");
                func_format_res(test_item);
                save_no_time = 0;
                return -1;
            }
            else
            {
                test_item->group[0].step--;
            }
        }
    }
    break;
    case 8: // 关闭看门狗
    {
        for (i = 0; i < test_item->group[2].line_count; i++)
        {
            func_send_frame_nr(tty_fd, test_item->group[2].line[i].content, strlen(test_item->group[2].line[i].content));
        }
        test_item->group[0].step++;
    }
    break;
    case 9: // 退出控制台
    {
        result = func_exit_console(&(test_para->sys_start), &(test_para->console), 100000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "can not exit console");
            func_format_res(test_item);
            save_no_time = 0;

            return 1;
        }
        else
        {
            test_item->group[0].step--;
        }
    }
    break;
    case 10: // 确认系统启动和登录
    {
        result = func_makesure_sys_start(&(test_para->sys_start), 100000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "system can not start");
            func_format_res(test_item);
            save_no_time = 0;
            return -1;
        }
        else
        {
            result = func_login_sys(&(test_para->sys_start));
            if (-1 == result)
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "system can not login");
                func_format_res(test_item);
                save_no_time = 0;
                return -1;
            }
            save_no_time = 0;
            return 1;
        }
    }
    break;
    default:
        return -1;
    }
    return 0;
}
/*
 * @description       : 模式6，系统起来后看门狗测试。
 * @param - *test_para: 测试参数
 * @param - items     : 测试哪一项
 * @param - groups    : 测试哪一组
 * @return		      : 1-正常 0-测试中 -1-测试异常，无法自恢复，退出测试
 */
int func_model_5_test(struct_test *test_para, struct_test_item *test_item, int *flag)
{
    // unsigned long diff_time;
    int result = -1;
    int wait_time = 0;

    switch (test_item->group[0].step)
    {
    case 0:
    {
        if (NULL == test_item->group[0].line[0].content)
        {
            void *send_data = calloc(1, 512);

            if (NULL == send_data)
            {
                printf("calloc failed  !!!!!!!\n");
                return -1;
            }
            unsigned long cur_time = func_get_system_time_ms();
            int dur_time = (int)(cur_time - test_item->one_start_time) / 1000;
            sprintf(send_data, "%s:%d;1;Tester cmd is empty,failed,cmd is empty;\n", test_item->title, dur_time);
            bsp_print_save("ERR", send_data);
            func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
            free(send_data);
            return -1;
        }
        // 将命令里的变量替换
        bsp_line_replace_var(test_item, 0, 0);

        test_item->group[0].line[0].status = 0;
        test_item->group[0].step++;
    }
    break;
    case 1:
    {
        // 执行命令
        bsp_sync_read_point();
        save_no_time = 1; // 命令交互期间存储的日志不加时间戳，因为会破坏原格式
        test->sys_start.need_init_tester = 1;
        printf("send cmd %s\n", test_item->group[0].line[0].content);
        func_send_frame_nr(tty_fd, test_item->group[0].line[0].content, strlen(test_item->group[0].line[0].content));
        test_item->group[0].step++;
    }
    break;
    case 2: // 等待重启
    {
        wait_time = (test_item->param[0].data_int * 1000) + 2000;
        result = func_get_one_str_time_diff(test_para->sys_start.first_strs[0], wait_time);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "watchdog not work");
            func_format_res(test_item);
        }
        else
        {
            // diff_time = func_get_system_time_ms();
            // diff_time = diff_time - test_item->process_time;
            wait_time = (test_item->param[0].data_int * 1000);
            if ((result < (wait_time - 1000)) || (result > (wait_time + 1000)))
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "watchdog time err");
                func_format_res(test_item);
            }
            else
            {
                strcpy(test_item->res[0].res, "passed");
                func_format_res(test_item);
            }
        }
        sleep(4);
        test_item->group[0].step++;
    }
    break;
    case 3: // 确认系统启动和登录
    {
        result = func_makesure_sys_start(&(test_para->sys_start), 100000);
        if (-1 == result)
        {
            strcpy(test_item->res[0].res, "failed");
            strcpy(test_item->res[0].res_reason, "system can not start");
            func_format_res(test_item);
            save_no_time = 0;
            return -1;
        }
        else
        {
            result = func_login_sys(&(test_para->sys_start));
            if (-1 == result)
            {
                strcpy(test_item->res[0].res, "failed");
                strcpy(test_item->res[0].res_reason, "system can not login");
                func_format_res(test_item);
                save_no_time = 0;
                return -1;
            }
            func_init_tester();
            test->sys_start.need_init_tester = 0;
            return 1;
        }
    }
    break;
    default:
        return -1;
    }
    return 0;
}
/*
 * @description       : 获取cpu的id。获取连续16个十六进制数据。
 * @param - *id       : cpu的id字符串
 * @param - *src      : 源字符串
 * @return		      : 0-已获得 -1-未获得
 */
int bsp_get_cpu_id(char *id, char *src)
{
    char *begin = NULL, *end = NULL;
    int len = 0;

    begin = src;
    end = src;
    len = strlen(src) + 1;
    while (len)
    {
        if (end - begin >= 16)
        {
            memcpy(id, begin, 16);
            printf("cpu_id=%s\n", id);
            return 0;
        }
        else
        {
            if ((*end) < '0')
                begin = (end + 1);
            else if (((*end) > '9') && ((*end) < 'A'))
                begin = (end + 1);
            else if (((*end) > 'F') && ((*end) < 'a'))
                begin = (end + 1);
            else if ((*end) > 'f')
                begin = (end + 1);
        }
        end++;
        len--;
    }
    return -1;
}
void bsp_str_A2a(char *src)
{
    int len = 0;
    len = strlen(src);
    while (len)
    {
        if (((*(src + len - 1)) > 'A') && ((*(src + len - 1)) < 'Z'))
            *(src + len - 1) += 0X20;
        len--;
    }
}

/*
 * @description         : 每个item测试。
 * @param - *test_item  : 测试一条item结构
 * @param - flag        : 0-续测 1-停止测试
 * @param - *one_item_stat: 0-测试中 1-测试完成
 * @param - human_operation_flag: 0-无 1-人机交互确认 2-人机交互不确认
 * @return		        : -1 暂停测试 0 正常 1 测试完成
 */
int func_one_item_test(struct_test_item *test_item, int *flag, int *one_item_stat, int human_operation_flag)
{
    *one_item_stat = 1;
    int result = -1;
    // 模式SYS_WATCHDOG，起机后自己初始化被测设备
    if (((test_item->group_cur == 0) && (test_item->group[test_item->group_cur].step == 0)) ||
        ((test->sys_start.need_init_tester) && (test_item->modle != SYS_WATCHDOG)))
    {
        if (-1 == func_set_sys_new_status(test, test_item->modle))
        {
            *one_item_stat = 0;
            return -1;
        }
    }

    switch (test_item->modle)
    {
    case ENTER_CONSOLE_STR:
        result = func_model_0_test(test, test_item, flag);
        break;
    case ENTER_CONSOLE_WAIT:
        result = func_model_1_test(test, test_item, flag);
        break;
    case CONSOLE_INTER:
        result = func_model_3_test(test, test_item, flag, human_operation_flag);
        break;
    case SYS_INTER:
        result = func_model_3_test(test, test_item, flag, human_operation_flag);
        break;
    case CONSOLE_WATCHDOG:
        result = func_model_4_test(test, test_item, flag);
        break;
    case SYS_WATCHDOG:
        result = func_model_5_test(test, test_item, flag);
        break;
    default:
        *one_item_stat = 0;
        return -1;
    }
    *one_item_stat = 0;
    return result;
}
/*
 * @description           : 清除上次测试结果
 * @param - *test_item    : item测试结构体
 * @return		          : 无
 */
void bsp_clean_one_item_test_result(struct_test_item *test_item)
{
    int i = 0, j = 0, k = 0;
    test_item->group_cur = 0;
    for (i = 0; i < test_item->group_count; i++)
    {
        for (j = 0; j < test_item->group[i].line_count; j++)
        {
            test_item->group[i].step = 0;
            for (k = 0; k < VALUE_NUMB; k++)
            {
                test_item->group[i].line[j].get_data[k].flag = 0;
                test_item->group[i].line[j].get_res[k].flag = 0;
                test_item->group[i].line[j].get_res_reason[k].flag = 0;
            }
        }
    }
    for (i = 0; i < VALUE_NUMB; i++)
    {
        memset(test_item->res, 0, sizeof(struct_res_class));
        memset(test_item->data, 0, sizeof(struct_data_class));
    }
    test_item->one_start_time = func_get_system_time_ms();
}
/*
 * @description           : 每个item测试线程
 * @param - *test_item    : item测试结构体
 * @return		          : -1 暂停测试 0 正常测试完成
 */
void *func_one_item_test_thread(void *arg)
{
    struct_test_item *current;
    int times = 0, result = 0;
    int triger_event = 0;
    int ne, ret, nevents = 0;
    int fd_temp = 0;
    short revents = 0;
    int inter_time = 0;
    unsigned long cur_time = 0;
    void *send_data = calloc(1, 512);
    int dur_time = 0;
    int one_item_stat = 0;
    int human_operation_flag = 0;
    int running_stat = CONTINUE_TEST;

    // printf("func_one_item_test_thread is start title=%s\n", ((struct_test_item *)arg)->title);
    if (NULL == send_data)
    {
        printf("calloc failed  !!!!!!!\n");
        goto __Tool_item_test_quit;
    }

    // 清除上次测试结果
    current = (struct_test_item *)arg;
    bsp_clean_one_item_test_result(current);
    current->all_start_time = func_get_system_time_ms();
    struct pollfd pollfds1[2] = {{test_manage_signal_fd[1], POLLIN, 0}, {manage_net_signal_fd[1], POLLIN, 0}};
    nevents = 2;
    while (1)
    {
        do
        {
            ret = poll(pollfds1, nevents, 50);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            // 停止测试
            running_stat = STOP_TEST;
            // 此处为测试线程异常退出，结果发送给服务器，不能确保处理回收组成功
            func_one_item_test(current, &running_stat, &one_item_stat, human_operation_flag);
            memset(send_data, 0, 512);
            cur_time = func_get_system_time_ms();
            dur_time = (int)(cur_time - current->one_start_time) / 1000;
            sprintf(send_data, "%s:%d;1;thread execute err,failed,thread execute err;\n", current->title, dur_time);
            bsp_print_save("ERR", send_data);
            func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
            goto __Tool_item_test_quit;
        }

        if ((ret == 0) && (one_item_stat == 0))
        {
            result = func_one_item_test(current, &running_stat, &one_item_stat, human_operation_flag);
            cur_time = func_get_system_time_ms();
            if (0 == result)
            {
                inter_time = cur_time - current->one_start_time;
                if (inter_time > current->timeout)
                {
                    // 停止测试
                    running_stat = STOP_TEST;
                    func_one_item_test(current, &running_stat, &one_item_stat, human_operation_flag);
                    // 整理该项测试超时，结果发送给服务器
                    memset(send_data, 0, 512);
                    dur_time = (int)(inter_time / 1000);
                    sprintf(send_data, "%s:%d;1;Tool or Tester execute timeout,failed,execute timeout;\n", current->title, dur_time);
                    bsp_print_save("ERR", send_data);
                    func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                    goto __Tool_item_test_quit;
                }
                else
                    continue;
            }
            if (1 == result)
            {
                if (running_stat != CONTINUE_TEST)
                {
                    goto __Tool_item_test_quit;
                }
                times++;
                inter_time = cur_time - current->all_start_time;
                if ((times >= current->times) && (inter_time >= current->dura))
                {
                    goto __Tool_item_test_quit;
                }
                else
                {
                    // 清除上次测试结果
                    bsp_clean_one_item_test_result(current);
                    continue;
                }
            }
            else if (-1 == result)
            {
                // 测试失败，结果发送给服务器
                goto __Tool_item_test_quit;
            }
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd_temp = pollfds1[ne].fd;
            revents = pollfds1[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
            {
                memset(send_data, 0, 512);
                cur_time = func_get_system_time_ms();
                dur_time = (int)(cur_time - current->one_start_time) / 1000;
                sprintf(send_data, "%s:%d;1;Tool or Tester execute timeout,failed,execute timeout;\n", current->title, dur_time);
                bsp_print_save("ERR", send_data);
                func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                goto __Tool_item_test_quit;
            }

            if ((revents & POLLIN) == 0)
                continue;
            if (fd_temp == test_manage_signal_fd[1])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    switch (triger_event)
                    {
                    case MANAGE_QUITE_EVENT:
                    case MANAGE_SUSPENSION_TEST:
                    case MANAGE_STOP_TEST:
                    {
                        running_stat = WAIT_RECOVER_TEST;
                        func_one_item_test(current, &running_stat, &one_item_stat, human_operation_flag);
                        // goto __Tool_item_test_quit;
                    }
                    break;
                    case MANAGE_SKIP_TEST:
                    {
                        running_stat = WAIT_RECOVER_TEST;
                        func_one_item_test(current, &running_stat, &one_item_stat, human_operation_flag);
                        memset(send_data, 0, 512);
                        cur_time = func_get_system_time_ms();
                        dur_time = (int)(cur_time - current->one_start_time) / 1000;
                        sprintf(send_data, "%s:%d;1;stop test,failed,manually skip the test;\n", current->title, dur_time);
                        bsp_print_save("ERR", send_data);
                        func_res_data_to_frame_queue(send_data, RES_TO_QUENE);
                        // goto __Tool_item_test_quit;
                    }
                    break;
                    default:
                        break;
                    }
                }
            }
            if (fd_temp == manage_net_signal_fd[1])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    switch (triger_event)
                    {
                    case NET_BACK_HUM_OPER_OK:
                    {
                        printf("human operation ok\n");
                        human_operation_flag = NET_BACK_HUM_OPER_OK;
                    }
                    break;
                    case NET_BACK_HUM_OPER_NG:
                    {
                        printf("human operation ng\n");
                        human_operation_flag = NET_BACK_HUM_OPER_NG;
                    }
                    break;
                    case NET_BACK_HUM_CONFIRM_OK:
                    {
                        printf("human confirm ok\n");
                        human_operation_flag = NET_BACK_HUM_CONFIRM_OK;
                    }
                    break;
                    case NET_BACK_HUM_CONFIRM_NG:
                    {
                        printf("human confirm ng\n");
                        human_operation_flag = NET_BACK_HUM_CONFIRM_NG;
                    }
                    break;
                    default:
                        break;
                    }
                }
            }
        }
    }
__Tool_item_test_quit:
    if (send_data)
        free(send_data);
    // 给主线程发送测试完成信号
    // 发送测试结果
    test_manage_send_event_to_main(MANAGE_TEST_COMPLETED);
    pthread_exit(NULL);
    return NULL;
}
/*
 * @description : 响应测试管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *tty_receive_manage(void *arg)
{
    int temp_rx_len = 0, need_write = 0;
    unsigned long tty_point = 0;
    struct pollfd pollfds[2] = {{tty_receive_signal_fd[1], POLLIN, 0}};
    int ne, ret, nevents = 1;
    int fd_temp = 0;
    short revents = 0;
    int i = 0;
    unsigned int err_code = 0;

    printf("tty_receive_manage is start\n");

    while (1)
    {
        do
        {
            ret = poll(pollfds, nevents, 1 * 10);
        } while ((ret < 0) && (errno == EINTR));
        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            break;
        }
        if (ret == 0)
        {
            temp_rx_len = func_receive_frame(tty_fd, &receive_buff[tty_point], 1024); /*读取串口收到的数据*/
            if (temp_rx_len > 0)
            {
                tty_point += temp_rx_len;
                tty_point %= MAX_RX_BUF_LEN;
                if ((receive_buff[tty_point - 1] == 0x0d) || (receive_buff[tty_point - 1] == 0x0a) || (receive_buff[tty_point - 1] == 0) || (receive_buff[tty_point - 1] == ':'))
                {
                    need_write = 1;
                }
            }
            else if (tty_point)
                need_write = 1;
            if (need_write)
            {
                if (print_debug)
                {
                    printf("[RX]:");
                    func_my_print(receive_buff, tty_point, 'c');
                }
                pthread_mutex_lock(&mutex);
                if (0 == save_no_time)
                    file_write_time_fmt(pLogFile, "[RX]:");
                file_write_data(pLogFile, receive_buff, tty_point);
                // printf("[RX]:");
                // func_my_print(receive_buff, tty_point, 'H');
                pthread_mutex_unlock(&mutex);

                for (i = 0; i < keep_check.keep_check_count; i++)
                {
                    if (strlen(keep_check.keep_check_str[i]) > 0)
                    {
                        if (strstr(receive_buff, keep_check.keep_check_str[i]) != NULL)
                        {
                            if ((0 == i) && (1 == test->sys_start.need_init_tester))
                                continue;
                            err_code = NET_ERR_B_BASE + i + 1;
                            test_manage_send_event_to_net(err_code);
                            // printf("err_code=%d\n", err_code);
                        }
                    }
                }
                tty_point = 0;
                need_write = 0;
            }
        }
        else
        {
            for (ne = 0; ne < nevents; ne++)
            {
                fd_temp = pollfds[ne].fd;
                revents = pollfds[ne].revents;

                if (revents & (POLLERR | POLLHUP | POLLNVAL))
                    goto __tty_receive_test_Thread_quit;

                if ((revents & POLLIN) == 0)
                    continue;
                if (fd_temp == tty_receive_signal_fd[1])
                {
                    int triger_event;
                    if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                    {
                        if (TTY_QUITE_EVENT == triger_event)
                            goto __tty_receive_test_Thread_quit;
                    }
                }
            }
        }
    }
__tty_receive_test_Thread_quit:
    printf("%s exit\n", __func__);
    pthread_exit(NULL);
    return NULL;
}