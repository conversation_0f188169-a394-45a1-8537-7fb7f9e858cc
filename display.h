#ifndef __DISPLAY_H__
#define __DISPLAY_H__

// 显示相关的宏定义
#define NONE                 "\e[0m"	 // 恢复默认
#define BLACK                "\e[0;30m"	 // 黑色
#define L_BLACK              "\e[1;30m"  // 加粗黑色
#define RED                  "\e[0;31m"  // 红色
#define L_RED                "\e[1;31m"  // 加粗红色
#define B_RED                "\e[5;31m"  // 闪烁红色
#define GREEN                "\e[0;32m"	 // 绿色
#define L_GREEN              "\e[1;32m"  // 加粗绿色
#define B_GREEN              "\e[5;32m"  // 闪烁绿色
#define BROWN                "\e[0;33m"	 // 棕色
#define YELLOW               "\e[1;33m"	 // 黄色
#define BLUE                 "\e[0;34m"	 // 蓝色
#define L_BLUE               "\e[1;34m"  // 加粗蓝色
#define PURPLE               "\e[0;35m"	 // 紫色
#define L_PURPLE             "\e[1;35m"  // 加粗紫色
#define CYAN                 "\e[0;36m"	 // 青色
#define L_CYAN               "\e[1;36m"  // 加粗青色
#define GRAY                 "\e[0;37m"	 // 灰色
#define WHITE                "\e[1;37m"	 // 白色

#define BOLD                 "\e[1m"	 // 加粗
#define UNDERLINE            "\e[4m"     // 下划线
#define BLINK                "\e[5m"     // 闪烁
#define REVERSE              "\e[7m"     // 反色
#define HIDE                 "\e[8m"     // 隐藏
#define CLEAR                "\e[2J"     // 清屏
#define CLRLINE              "\r\e[K"    // 清行
#define MOVECUR              "\e[1A"     // 光标上移1行 

#pragma pack(1) //字节对齐
#pragma pack() 

#endif //__DISPLAY_H__