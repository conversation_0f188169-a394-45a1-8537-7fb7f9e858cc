#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <sys/time.h>
#include <sys/epoll.h>
#include <poll.h>
#include <pthread.h>
#include <time.h>
#include "net.h"
#include "manage.h"
#include "file.h"

#define MAX_RX_DATA_LEN 2048
#define ONCE_RX_LEN 300
char net_rx_buff[MAX_DATA_LEN] = {0};
int net_read_point = 0, net_write_point = 0;
char net_rx_frame[300] = {0};
struct_resend BackData;
struct_send_to_net send_to_net;
char human_operation[100] = {0};

/*
 * @description  : 检测TCP客户端连接状态
 * @param - fd   : 文件描述符
 * @return		 : 执行结果 链接断返回-1 正常返回0
 */
int func_detect_tcp_client_link(int fd)
{
    int info_len = 0, re_err = 0;
    struct tcp_info tcp_infos;

    memset(&tcp_infos, 0, sizeof(tcp_infos));

    info_len = sizeof(struct tcp_info);

    getsockopt(fd, IPPROTO_TCP, TCP_INFO, (void *)&tcp_infos, (socklen_t *)&info_len);
    if (tcp_infos.tcpi_state != TCP_ESTABLISHED)
    {
        re_err = -1;
    }

    return re_err;
}

/*
 * @description  : TCP客户端与服务端建立链接
 * @param - *fd  : 链接文件描述符
 * @param - *ip  : 服务端ip，
 * @param - port : 服务端端口，
 * @return		 : 执行结果 链接成功返回0 连接失败返回-1
 */
int func_create_tcp_client_link(int *fd, char *ip, unsigned int port)
{
    int value = 0, Flags = 0;
    struct sockaddr_in servaddr;
    fd_set set;
    struct timeval timeout;

    // 设置建立连接等待时间
    timeout.tv_sec = 30;
    timeout.tv_usec = 0;

    /*(1) 创建套接字*/
    if ((*fd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
    {
        value = -1;
        return value;
    }

    /*(2) 设置链接服务器地址结构*/
    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_addr.s_addr = inet_addr(ip);
    servaddr.sin_port = htons(port);

    // 设置为非阻塞
    Flags = fcntl(*fd, F_GETFL, 0);
    fcntl(*fd, F_SETFL, Flags | O_NONBLOCK);

    /*(3) 发送链接服务器请求*/
    if (connect(*fd, (struct sockaddr *)&servaddr, sizeof(servaddr)) < 0)
    {
        FD_ZERO(&set);
        FD_SET(*fd, &set);
        if (select((*fd) + 1, NULL, &set, NULL, &timeout) > 0)
        {
            if (func_detect_tcp_client_link(*fd) == -1)
                return -1;
            else
                value = 0;
        }
        else
        {
            value = -1;
            return value;
        }
    }
    return value;
}

/*
 * @description  : TCP客户端接收数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_receive_buff: 接收数据缓冲区首地址
 * @param - count: 最大接收长度
 * @return		 : 实际接收数据长度
 */
int func_tcp_client_receive(int fd, char *p_receive_buff, int count)
{
    int receive_numb = 0;

    memset(p_receive_buff, 0x0, count);
    receive_numb = recv(fd, p_receive_buff, count, 0);

    return receive_numb;
}

/*
 * @description  : TCP客户端发送数据函数
 * @param - fd   : 链接文件描述符
 * @param - *p_send_buff: 发送数据缓冲区首地址
 * @param - count: 发送数据长度
 * @return		 : 实际发送数据长度
 */
int func_tcp_client_send(int fd, char *p_send_buff, int count)
{
    int send_numb = 0;

    if (func_detect_tcp_client_link(fd) != 0)
    {
        printf("ReadCwnd judge connect braek\n");
        return -1;
    }
    send_numb = write(fd, p_send_buff, count);
    if (count != send_numb)
    {
        printf("server terminated prematurely write err\n");
        return -1;
    }
    return send_numb;
}

/*
 * @description  : TCP客户端关闭链接
 * @param - fd   : 链接文件描述符
 * @return		 : 无
 */
void func_close_tcp_client_link(int fd)
{
    if (fd > 0)
    {
        close(fd);
    }
}
/*
 * @description       : 对时功能
 * @param - timeinfo  : 新时间
 * @return		      : 无
 */
void bsp_set_time(struct tm timeinfo)
{
    struct timeval tv;
    tv.tv_sec = mktime(&timeinfo);
    tv.tv_usec = 0;

    if (settimeofday(&tv, NULL) < 0)
    {
        perror("settimeofday");
    }
    return;
}
/*
 * @description  : 报文组帧
 * @param - src  : 原数据
 * @param - dest : 目的数据
 * @param - fram_flag  : 是否分帧0-不分 1-分且后续还有帧 2-分帧且为最后一帧
 * @param - fram_num : 帧序号
 * @return		 : 无
 */
int bsp_frame_organaize(char *src, char *dest, int fram_flag, int fram_num)
{
    int i = 0;
    int len = 0;

    len = strlen(src);
    dest[i++] = 0x55;
    dest[i++] = 0xaa;
    dest[i++] = fram_flag;
    dest[i++] = fram_num;
    dest[i++] = len & 0xff;
    dest[i++] = (len >> 8) & 0xff;
    memcpy(&dest[i], src, len);
    i += len;
    dest[i++] = 0xa5;

    return i;
}
/*
 * @description   : 网络连接管理线程
 * @param - *fram : 数据
 * @param - len   : 数据长度
 * @return		  : 无
 */
void bsp_deal_resend_data(char *fram, int len)
{
    if (len < (MAX_BACKDATA_LEN - BackData.len))
    {
        memcpy(&BackData.data[BackData.len], fram, len);
        BackData.len += len;
        BackData.resend_flag = 1;
    }
}
/*
 * @description : 网络连接管理线程
 * @param - arg : 参数
 * @return		: 无
 */
void *net_inter_manage(void *arg)
{
    int result = 0;
    int sockfd = 0;
    int connect_state = DISCONNECT;
    int wait_time = 500; // 默认每个函数间隔200ms
    int triger_event = 0;
    int signo;
    char temp_buff[2000] = {0};
    char fram_buff[512] = {0};
    char time_char[50] = {0};
    int len = 0, value = 0;
    int net_rx_len = 0, change_len = 0, temp_len = 0, i = 0;
    int fd = 0;
    short revents = 0;

    // 跟服务器建立TCP链接
    result = func_create_tcp_client_link(&sockfd, "***********", 8000);
    if (result >= 0)
    {
        connect_state = CONNECT;
        net_read_point = 0;
        net_write_point = 0;
        printf("tcp connected\n");
        net_inter_send_event_to_main(NET_CONNECT_OK);
    }
    else
    {
        printf("tcp connect failed\n");
        net_inter_send_event_to_main(NET_CONNECT_FAIL);
    }

    while (1)
    {
        struct pollfd pollfds[] = {{net_inter_signal_fd[1], POLLIN, 0}, {manage_net_signal_fd[0], POLLIN, 0}, {sockfd, POLLIN, 0}};
        int ne, ret, nevents = 0;
        if (sockfd > 0)
            nevents = sizeof(pollfds) / sizeof(pollfds[0]);
        else
            nevents = 2;

        do
        {
            ret = poll(pollfds, nevents, wait_time);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)\n", __func__, ret, errno, strerror(errno));
            net_inter_send_event_to_main(NET_THREAD_QUITE);
            goto __net_manage_quit;
        }
        if (ret == 0)
        {
            if (connect_state == DISCONNECT)
            {
                // TCP做客户端，主动跟服务端建立链接
                result = func_create_tcp_client_link(&sockfd, "***********", 8000);
                if (result >= 0)
                {
                    connect_state = CONNECT;
                    net_read_point = 0;
                    net_write_point = 0;
                    printf("tcp connected\n");
                    net_inter_send_event_to_main(NET_CONNECT_OK);
                    memset(temp_buff, 0, sizeof(temp_buff));
                    sprintf(temp_buff, "ID:%s", tool_ID);
                    printf("temp_buff=%s\n", temp_buff);
                    len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                    if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                    {
                        bsp_deal_resend_data(fram_buff, len);
                    }
                }
            }
            else
            {
                // 检测TCP链接状态
                result = func_detect_tcp_client_link(sockfd);
                if (result < 0)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    printf("ReadCwnd judge connect braek\n");
                    // TCP做客户端，主动跟服务端建立链接
                    result = func_create_tcp_client_link(&sockfd, "***********", 8000);
                    if (result >= 0)
                    {
                        connect_state = CONNECT;
                        net_read_point = 0;
                        net_write_point = 0;
                        printf("tcp connected\n");
                        memset(temp_buff, 0, sizeof(temp_buff));
                        sprintf(temp_buff, "ID:%s", tool_ID);
                        printf("temp_buff=%s\n", temp_buff);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    else
                    {
                        net_inter_send_event_to_main(NET_CONNECT_FAIL);
                    }
                }
            }
            if ((1 == BackData.resend_flag) && (connect_state == CONNECT))
            {
                if (-1 == func_tcp_client_send(sockfd, BackData.data, BackData.len))
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    net_inter_send_event_to_main(NET_SEND_FAIL);
                }
                else
                {
                    BackData.resend_flag = 0;
                    BackData.len = 0;
                    send_to_net.flag = 0;
                }
            }
            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd = pollfds[ne].fd;
            revents = pollfds[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
            {
                printf("%s poll err/hup", __func__);
                printf("epoll fd = %d, events = 0x%04x\n", fd, revents);
                if (fd == sockfd)
                {
                    connect_state = DISCONNECT;
                    close(sockfd);
                    sockfd = 0;
                    // printf("TCP连接异常，将在下一个循环尝试重连\n");
                    continue;
                }
                else if (revents & POLLHUP)
                {
                    net_inter_send_event_to_main(NET_THREAD_QUITE);
                    goto __net_manage_quit;
                }
            }

            if ((revents & POLLIN) == 0)
                continue;

            if (fd == net_inter_signal_fd[1])
            {
                if (read(fd, &signo, sizeof(signo)) == sizeof(signo))
                {
                    memset(temp_buff, 0, sizeof(temp_buff));
                    switch (signo)
                    {
                    case NET_QUITE_EVENT:
                    {
                        value = NET_ERR_A7 - NET_ERR_A_BASE;
                        Func_Time_GetSystemTime_ToChar(time_char);
                        sprintf(temp_buff, "Err:%s err reason code:A%d", (time_char + 1), value);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        func_tcp_client_send(sockfd, fram_buff, len);
                        sleep(1);
                        goto __net_manage_quit;
                    }
                    break;
                    case NET_NORMAL_QUITE_EVENT:
                    {
                        goto __net_manage_quit;
                    }
                    break;
                    case NET_SEND_ID:
                    {
                        sprintf(temp_buff, "ID:%s", tool_ID);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_HEARTBEAT:
                    {
                        printf("tool send heartbeat.\n");
                        len = bsp_frame_organaize("Cmd:Heartbeat", fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEN_TESTER_INFO:
                    {
                        sprintf(temp_buff, "Msg:%s,%s,%s,%s,%.2f,%.2f", info.factory, info.platform, info.kernel_version, info.uboot_version, info.mem_size, info.flash_size);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_TESTER_CHG_CNTE_OR_NOT:
                    {
                        sprintf(temp_buff, "Cmd:%s", "被测设备已变更，是否删除之前测试结果，重新开始测试");
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_REQUST_COUNTINUE_TEST:
                    {
                        sprintf(temp_buff, "Cmd:%s", "工装请求继续测试");
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_PARAM_OK:
                    {
                        sprintf(temp_buff, "Cmd:%s", "参数下载成功");
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_PARAM_ERR:
                    {
                        sprintf(temp_buff, "Cmd:%s", "参数下载失败");
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_ERR_A1:
                    case NET_ERR_A2:
                    case NET_ERR_A3:
                    case NET_ERR_A4:
                    case NET_ERR_A5:
                    case NET_ERR_A6:
                    case NET_ERR_A8:
                    {
                        value = signo - NET_ERR_A_BASE;
                        Func_Time_GetSystemTime_ToChar(time_char);
                        sprintf(temp_buff, "Err:%s err reason code:A%d", (time_char + 1), value);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_RES:
                        sprintf(temp_buff, "Res:%s", send_to_net.data);
                        len = bsp_frame_organaize(temp_buff, fram_buff, send_to_net.frame_flag, send_to_net.frame_num);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                        else
                            send_to_net.flag = 0;

                        break;
                    case NET_SEND_DATA:
                        sprintf(temp_buff, "Data:%s", send_to_net.data);
                        len = bsp_frame_organaize(temp_buff, fram_buff, send_to_net.frame_flag, send_to_net.frame_num);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                        else
                            send_to_net.flag = 0;

                        break;
                    case NET_SEND_TEST_END:
                        sprintf(temp_buff, "Cmd:%s,%s", "测试完成", put_log_path);
                        // printf("temp_buff=%s\n", temp_buff);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            printf("send test end failed\n");
                            bsp_deal_resend_data(fram_buff, len);
                        }
                        else
                        {
                            printf("send test end success\n");
                            send_to_net.flag = 0;
                        }

                        break;

                    default:
                        break;
                    }
                }
            }
            if (fd == manage_net_signal_fd[0])
            {
                if (read(fd, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    switch (triger_event)
                    {
                    case NET_ERR_A9:
                    {
                        value = triger_event - NET_ERR_A_BASE;
                        Func_Time_GetSystemTime_ToChar(time_char);
                        sprintf(temp_buff, "Err:%s err reason code:A%d", (time_char + 1), value);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_ERR_B1:
                    case NET_ERR_B2:
                    case NET_ERR_B3:
                    {
                        value = triger_event - NET_ERR_B_BASE;
                        Func_Time_GetSystemTime_ToChar(time_char);
                        sprintf(temp_buff, "Err:%s err reason code:B%d", (time_char + 1), value);
                        printf("%s\n", temp_buff);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                    }
                    break;
                    case NET_SEND_HUM_OPER:
                        sprintf(temp_buff, "Cmd:%s:%s", "Human operation", human_operation);
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                        else
                            send_to_net.flag = 0;

                        break;
                    case NET_SEND_HUM_CONFIRM:
                        sprintf(temp_buff, "Cmd:%s:%s", "Human operation", "请确认结果");
                        len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                        if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                        {
                            bsp_deal_resend_data(fram_buff, len);
                        }
                        break;

                    default:
                        break;
                    }
                }
            }
            // 网络数据接收
            if (fd == sockfd)
            {
                char *p_temp = NULL;
                if (MAX_RX_DATA_LEN - net_write_point < ONCE_RX_LEN)
                {
                    net_rx_len = func_tcp_client_receive(sockfd, &net_rx_buff[net_write_point], (MAX_RX_DATA_LEN - net_write_point));
                    net_rx_len += func_tcp_client_receive(sockfd, &net_rx_buff[0], (ONCE_RX_LEN + net_write_point - MAX_RX_DATA_LEN));
                }
                else
                {
                    net_rx_len = func_tcp_client_receive(sockfd, &net_rx_buff[net_write_point], ONCE_RX_LEN);
                }
                if (net_rx_len > 0)
                {
                    net_write_point += net_rx_len;
                    net_write_point %= MAX_RX_DATA_LEN;

                    while (net_read_point != net_write_point)
                    {
                        if (net_read_point > net_write_point)
                            change_len = MAX_RX_DATA_LEN + net_write_point - net_read_point;
                        else
                            change_len = net_write_point - net_read_point;
                        if (change_len < 10)
                        {
                            break;
                        }
                        // 解析帧
                        if (net_rx_buff[net_read_point] == 0x55)
                        {
                            if (net_rx_buff[(net_read_point + 1) % MAX_RX_DATA_LEN] == 0xaa)
                            {
                                temp_len = net_rx_buff[(net_read_point + 4) % MAX_RX_DATA_LEN] + (net_rx_buff[(net_read_point + 5) % MAX_RX_DATA_LEN] << 8);
                                if (temp_len < 0 || temp_len > 2000)
                                {
                                    net_read_point++;
                                    net_read_point %= MAX_RX_DATA_LEN;
                                    continue;
                                }
                                if (net_rx_buff[(net_read_point + 6 + temp_len) % MAX_RX_DATA_LEN] == 0xa5)
                                {
                                    memset(net_rx_frame, 0, sizeof(net_rx_frame));
                                    for (i = 0; i < temp_len; i++)
                                    {
                                        net_rx_frame[i] = net_rx_buff[(net_read_point + 6 + i) % MAX_RX_DATA_LEN];
                                    }
                                    func_my_print(net_rx_frame, temp_len, 'c');
                                    // 解析帧
                                    if (strncmp(net_rx_frame, "Cmd:", 4) == 0)
                                    {
                                        // printf("net_rx_frame=%s\n", net_rx_frame);
                                        if (strstr(net_rx_frame, "Time sync:") != NULL)
                                        {
                                            struct tm timeinfo;
                                            sscanf(net_rx_frame, "Cmd:Time sync:%d-%d-%d %d:%d:%d", &timeinfo.tm_year, &timeinfo.tm_mon, &timeinfo.tm_mday, &timeinfo.tm_hour, &timeinfo.tm_min, &timeinfo.tm_sec);
                                            timeinfo.tm_year -= 1900;
                                            timeinfo.tm_mon -= 1;
                                            bsp_set_time(timeinfo);

                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "ACK") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_ACK);
                                        }
                                        else if (strstr(net_rx_frame, "ID is not in the list") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_ID_ERR);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if ((p_temp = strstr(net_rx_frame, "Update param:")) != NULL)
                                        {
                                            p_temp += strlen("Update param:");
                                            sscanf(p_temp, "%[^,],%s", param_file, param_file_md5);
                                            net_inter_send_event_to_main(NET_BACK_PARAM_PATH);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Start test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_START_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Stop test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_STOP_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Restart test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_RESTART_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Suspension test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_SUSPENSION_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Continue test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_CONTINUE_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Skip current item test") != NULL)
                                        {
                                            net_inter_send_event_to_main(NET_BACK_SKIP_CURRENT_TEST);
                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Human operation:") != NULL)
                                        {
                                            if (strstr(net_rx_frame, "get ready") != NULL)
                                            {
                                                net_send_event_to_test_manage(NET_BACK_HUM_OPER_OK);
                                            }
                                            else if (strstr(net_rx_frame, "not good") != NULL)
                                            {
                                                net_send_event_to_test_manage(NET_BACK_HUM_OPER_NG);
                                            }
                                            else if (strstr(net_rx_frame, "passed") != NULL)
                                            {
                                                net_send_event_to_test_manage(NET_BACK_HUM_CONFIRM_OK);
                                            }
                                            else if (strstr(net_rx_frame, "failed") != NULL)
                                            {
                                                net_send_event_to_test_manage(NET_BACK_HUM_CONFIRM_NG);
                                            }

                                            sprintf(temp_buff, "Cmd:ACK;%s", net_rx_frame);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        else if (strstr(net_rx_frame, "Get ID") != NULL)
                                        {
                                            sprintf(temp_buff, "ID:%s", tool_ID);
                                            len = bsp_frame_organaize(temp_buff, fram_buff, 0, 0);
                                            if (-1 == func_tcp_client_send(sockfd, fram_buff, len))
                                            {
                                                bsp_deal_resend_data(fram_buff, len);
                                            }
                                        }
                                        net_read_point += temp_len + 7;
                                        net_read_point %= MAX_RX_DATA_LEN;
                                    }
                                    else
                                    {
                                        net_read_point++;
                                        net_read_point %= MAX_RX_DATA_LEN;
                                    }
                                }
                                else
                                {
                                    net_read_point++;
                                    net_read_point %= MAX_RX_DATA_LEN;
                                }
                            }
                            else
                            {
                                net_read_point++;
                                net_read_point %= MAX_RX_DATA_LEN;
                            }
                        }
                        else
                        {
                            net_read_point++;
                            net_read_point %= MAX_RX_DATA_LEN;
                        }
                    }
                }
            }
        }
    }
__net_manage_quit:
    printf("%s exit", __func__);

    if (sockfd > 0)
    {
        close(sockfd);
    }
    pthread_exit(NULL);
    return NULL;
}
