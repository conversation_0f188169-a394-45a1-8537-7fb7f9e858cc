ifneq ($(CROSS_COMPILE),)
CROSS-COMPILE:=$(CROSS_COMPILE)
endif
ifeq ($(CC),cc)
CC:=$(CROSS-COMPILE)gcc
endif
LD:=$(CROSS-COMPILE)ld

LIBCONFIG_SRC=grammar.c libconfig.c scanctx.c scanner.c strbuf.c strvec.c util.c wincompat.c
LIBCONFIG_OBJ=$(LIBCONFIG_SRC:.c=.o)

CFLAGS += -Wall -Wextra -O1 #-s
LDFLAGS += -lpthread -ldl -lrt


libconfig.a: $(LIBCONFIG_OBJ)
	$(AR) rcs libconfig.a $(LIBCONFIG_OBJ)

clean:
	rm -rf *.o libconfig.a