#include <stdio.h>

#define CONNECT 1
#define DISCONNECT 0

#define CREATE_TURE 1
#define CREATE_FALSE 0
#define MAX_DATA_LEN 2048

/*网络参数结构体 */
typedef struct
{
    char ip[16];              /*目的IP*/
    unsigned int port;        /*目的端口*/
    unsigned int listen_port; /*目的端口*/
} struct_net_param;

int func_detect_tcp_client_link(int fd);
int func_create_tcp_client_link(int *fd, char *ip, unsigned int port);
int func_tcp_client_receive(int fd, char *p_receive_buff, int count);
int func_tcp_client_send(int fd, char *p_send_buff, int count);
void func_close_tcp_client_link(int fd);
