# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#3568
#CC=aarch64-linux-gnu-gcc
#d9
# CC=/tool/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc


LDFLAGS = ./libconfig
auto_test:main.o config_file.o file.o manage.o one_title.o pidfile.o serial.o tcp_client.o md5.o
	$(CC)	-Wall	main.o config_file.o file.o manage.o one_title.o pidfile.o serial.o tcp_client.o md5.o -o  auto_test -lpthread -I$(LDFLAGS) -L$(LDFLAGS) -lconfig
main.o:main.c config_file.h file.h pidfile.h manage.h serial.h
	$(CC)	-c	-Wall	main.c	-o	main.o -lpthread
config_file.o:config_file.c config_file.h
	$(CC)	-c	-Wall	config_file.c	-o	config_file.o -I$(LDFLAGS) -L$(LDFLAGS) -lconfig
file.o:file.c file.h
	$(CC)	-c	-Wall	file.c	-o	file.o
manage.o:manage.c manage.h file.h serial.h md5.h
	$(CC)	-c	-Wall	manage.c	-o	manage.o
one_title.o:one_title.c manage.h file.h serial.h md5.h
	$(CC)	-c	-Wall	one_title.c	-o	one_title.o
pidfile.o:pidfile.c pidfile.h
	$(CC)	-c	-Wall	pidfile.c	-o	pidfile.o
serial.o:serial.c serial.h
	$(CC)	-c	-Wall	serial.c	-o	serial.o
tcp_client.o:tcp_client.c net.h
	$(CC)	-c	-Wall	tcp_client.c	-o	tcp_client.o
md5.o:md5.c md5.h
	$(CC)	-c	-Wall	md5.c	-o	md5.o

clean:
	$(RM) *.o	auto_test

